import { Components, Theme } from '@mui/material';

export default function Autocomplete(theme: Theme): Components<Theme> {
  return {
    MuiAutocomplete: {
      styleOverrides: {
        paper: {
          boxShadow: theme.customShadows.dropdown,
        },
        listbox: {
          padding: theme.spacing(0, 1),
          '& .MuiAutocomplete-option': {
            padding: theme.spacing(1),
            margin: theme.spacing(1, 0),
            borderRadius: theme.shape.borderRadius,
          },
        },
        input: {
          // height: 29.5,
          height: 5,
        },
      },
    },
  };
}
