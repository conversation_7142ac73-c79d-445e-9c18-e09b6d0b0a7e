import { Box } from '@mui/material';
import VideoPlayer from '../Dashboard/Videoplayer';

type PopupVideoPlayerProps = {
  videoFile: string;
};

const PopupVideoPlayer = ({ videoFile }: PopupVideoPlayerProps) => {
  return (
    <Box height={250} px={3} pt={1}>
      <VideoPlayer
        url={
          /\.(mp4|mov|avi|mkv)$/.test(videoFile)
            ? // `http://demo.passdaily.in/EventFile/${videoFile}`
              `http://holy.passdaily.in/EventFile/${videoFile}`
            : // `http://carmel.passdaily.in/EventFile/${videoFile}`
              // `http://therese.passdaily.in/EventFile/${videoFile}`
              // `http://stm.passdaily.in/EventFile/${videoFile}`
              // `http://nirmala.passdaily.in/EventFile/${videoFile}`
              `https://www.youtube.com/watch?${videoFile}`
        }
        thumbnail=""
      />
    </Box>
  );
};
export default PopupVideoPlayer;
