/* eslint-disable no-nested-ternary */
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  Typography,
  TableHead,
  TableRow,
  Button,
  Stack,
  useTheme,
  IconButton,
} from '@mui/material';
import React, { ReactElement, useCallback, useRef } from 'react';
import SuccessPaid from '@/assets/ManageFee/successPaid.svg';
import CloseIcon from '@mui/icons-material/Close';
import paidSuccess from '@/assets/ManageFee/paidSuccess.json';
import errorIcon from '@/assets/ManageFee/Error.json';
import { useReactToPrint } from 'react-to-print';
import { StudentFeeStatusDataType, StudentTermFeePayType } from '@/types/ManageFee';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import { studentTermFeePay } from '@/store/ManageFee/manageFee.thunks';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import LoadingButton from '@mui/lab/LoadingButton';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getManageFeeSubmitting } from '@/config/storeSelectors';
import dayjs from 'dayjs';
import styled from 'styled-components';
import passdailLogo from '@/assets/SchoolLogos/logo-small.svg';
import holyLogo from '@/assets/SchoolLogos/HolyLogo.jpeg';
import carmelLogo from '@/assets/SchoolLogos/CarmelLogo.png';
import thereseLogo from '@/assets/SchoolLogos/StthereseLogo.png';
import thomasLogo from '@/assets/SchoolLogos/StThomasLogo.png';
import MIMLogo from '@/assets/SchoolLogos/MIMLogo.png';
import nirmalaLogo from '@/assets/SchoolLogos/NirmalaLogo.png';
import useSettings from '@/hooks/useSettings';

// Define CSS styles for print in your component file or in an external CSS file
const printStyles2 = `
@media print {
.Main-div{
margin-top:0px;
}
.amount_words{
max-width:95%;
}
  .print-preview-container {
    width: 95%;
height:96.5%;
position:absolute;
top:0px;
bottom:0px;
margin: 20px;
padding:20px;
border: 1px solid;
  }
  .scrollbar::-webkit-scrollbar{
    width:0px
  }
}
`;

// Inject the styles into the document head
const styleSheet = document.createElement('style');
styleSheet.type = 'text/css';
styleSheet.innerText = printStyles2;
document.head.appendChild(styleSheet);

type TermFeeStatsProps = {
  termId: number;
  amount: number;
  scholarship: number | string;
  discount: number;
  fine: number;
  paid: number;
  balance: number;
};
export type CellDataType = {
  feeId: number;
  termId: number;
  feeTitle: string;
  termTitle: string;
  amount: number;
  scholarship: number;
  discount: number;
  fine: number;
  termFeeStats?: TermFeeStatsProps[];
};
type PaymentmodeType = {
  paymentTypeId: number;
  amount: number;
  chequeOrDDNo: string;
  dateOfIssue: string;
  deppositDate: string;
  clearingDate: string;
  bankId: number;
  payStatus: string;
};

type PaymentTypesType = {
  paymentTypeId: number;
  paymentType: string;
};

type PayPrps = {
  feeStatusData: CellDataType[];
  payDetails: StudentTermFeePayType;
  onClose: any;
  load: any;
  studentDetails: StudentFeeStatusDataType;
  payDate: any;
  setPayDetails: any;
  onCancel: () => void;
  receiptType?: string;
  paymentTypes: PaymentTypesType[];
};
export const Pay = ({
  feeStatusData,
  payDetails,
  onClose,
  studentDetails,
  payDate,
  load,
  onCancel,
  receiptType,
  paymentTypes,
}: PayPrps) => {
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const isSubmitting = useAppSelector(getManageFeeSubmitting);
  const [printButtonShow, setPrintButtonShow] = React.useState<boolean>(false);
  const [receiptNumber, setReceiptNumber] = React.useState<number>();
  const [paymodeData, setPaymodeData] = React.useState<PaymentmodeType[]>([]);
  const [resetAll, setResetAll] = React.useState(false);
  const [grandTotalInWords, setGrandTotalWords] = React.useState('');

  const componentRef = useRef<HTMLInputElement>(null);
  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
  });

  // Calculate total amount paid
  const totalAmountPaid = feeStatusData?.reduce((total, item) => total + item.amount, 0);

  const showConfirmation = useCallback(
    async (successMessage: ReactElement, title: string) => {
      const confirmation = confirm(successMessage, title, {
        okLabel: 'Ok',
        showOnlyOk: true,
      });

      return confirmation;
    },
    [confirm]
  );
  React.useEffect(() => {
    console.log('payDetails----::::', payDetails);
    const paymode = payDetails.paymentmode ? payDetails.paymentmode.map((item) => ({ ...item })) : [];
    setPaymodeData(paymode);
  }, [payDetails, payDate]);

  const handlePay = useCallback(async () => {
    try {
      const actionResult = await dispatch(studentTermFeePay(payDetails)).unwrap();

      if (actionResult) {
        setResetAll(true);
        // onClose();
        // const results = actionResult.payload;
        const { receiptNo, grandTotalWords } = actionResult;
        setReceiptNumber(receiptNo);
        setGrandTotalWords(grandTotalWords);
        console.log('actionResult::::----', actionResult);
        await showConfirmation(
          <SuccessMessage icon="" loop={false} jsonIcon={paidSuccess} message="Paid successfully" />,
          ''
        );
        // onCancel();
        load();
        setPrintButtonShow(true);
      } else {
        await showConfirmation(<ErrorMessage jsonIcon={errorIcon} message="Payment Failed" />, '');
      }
      // const successMessage = <SuccessMessage icon={AddFile} message="Message created successfully" />;
      // await confirm(successMessage, 'Message Created', { okLabel: 'Ok', showOnlyOk: true });
    } catch (error) {
      await showConfirmation(<ErrorMessage icon="" message="Something went wrong please try again" />, '');
      console.error(error);
    }
  }, [dispatch, payDetails, showConfirmation, load]);
  const formattedDate = dayjs().format('DD/MM/YYYY');
  return (
    <Box>
      <Box className="print-preview-container" ref={componentRef}>
        <Box flexDirection="column" display="flex" alignItems="center" mb={receiptType ? 2 : 0}>
          {/* <img src={passdailLogo} width={70} alt="passdailLogo" /> */}
          {receiptType === 'ReceiptDefault' && <img src={holyLogo} width={70} alt="holyLogo" />}
          {/* <img src={carmelLogo} width={70} alt="carmelLogo" /> */}
          {/* <img src={thereseLogo} width={70} alt="thereseLogo" /> */}
          {/* <img src={thomasLogo} width={70} alt="thomasLogo" /> */}
          {/* <img src={nirmalaLogo} width={70} alt="thomasLogo" /> */}
          {/* <img src={MIMLogo} width={70} alt="MIMLogo" /> */}
          {/* ============ Passdaily =========== */}
          {/* <Typography variant="subtitle2" fontSize={16} color="primary">
            Passdaily
          </Typography>
          <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
            Palakad, Kerala.
          </Typography> */}
          {/* ============ MIM =========== */}
          {/* <Typography variant="subtitle2" fontSize={16} color="primary">
            Mueenul Islam Manoor High School
          </Typography>
          <Typography variant="body1" fontSize={13} color="secondary">
            KANDANAKAM,KALADI PO MALAPPURAM 679582
          </Typography>
          <Typography variant="body1" fontSize={13} color="secondary">
            04942103095,9645942121
          </Typography> */}
          {/* ============ Holy Angels =========== */}
          {receiptType === 'ReceiptDefault' && (
            <>
              <Typography variant="subtitle2" fontSize={16} color="primary">
                Holy Angels Paradise
              </Typography>
              <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
                Dombivli, Mumbai, Maharashtra.
              </Typography>
            </>
          )}
          {/* ============ Carmel Convent =========== */}
          {/* <Typography variant="subtitle2" fontSize={16} color="primary">
            Carmel Convent School
          </Typography>
          <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
            Bhanvaj road , Khopoli - 410 203, Dist. <br /> Raigad, Maharashtra.
          </Typography>
          <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
            <EMAIL> , +919763642887
          </Typography> */}
          {/* ============ St Therese =========== */}
          {/* <Typography variant="subtitle2" fontSize={16} color="primary">
            St Therese Convent High School
          </Typography>
          <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
            Nirmalwadi, Kharadi -411014,
            <br /> Tal- Haveli, Dist- Pune
          </Typography>
          <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
            <EMAIL>, 9765645776
          </Typography> */}
          {/* ============ St Thomas =========== */}
          {/* <Typography variant="subtitle2" fontSize={16} color="primary">
            St Thomas School
          </Typography>
          <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
            Behind Ebenzer Church, Near Shankeshwar Nagar
            <br />
            Nandivil-Bhoper Road, Dombivli(E) - 421 201
          </Typography>
          <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
            <EMAIL>, 8928851277
          </Typography> */}
          {/* ============ Nirmal Convent =========== */}
          {/* <Typography variant="subtitle2" fontSize={16} color="primary">
            Nirmala Convent School
          </Typography>
          <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
            Nirmalwadi, Kharadi -411014,
            <br /> Tal- Haveli, Dist- Pune
          </Typography>
          <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
            <EMAIL>, 9765645776
          </Typography> */}
          {/* ========================= */}
        </Box>
        {/* ========== Holy Angels Gardens ========== */}
        {receiptType === 'ReceiptHolyNursery' && (
          <Box
            className="Main-div"
            flexDirection="row"
            display="flex"
            mt={5}
            gap={4}
            justifyContent="center"
            alignItems="center"
            p={2}
            mb={2}
            bgcolor="#e2b4bb"
          >
            <img src={holyLogo} width={100} alt="holyLogo" />
            <Stack textAlign="center" flex={1}>
              <Typography variant="h1" fontWeight="bold" fontSize={30} color="primary">
                <b>ANGELS&apos; GARDENS</b>
              </Typography>
              <Typography variant="body1" fontSize={13}>
                The Kindergarten Experience
              </Typography>
              <Typography variant="body1" fontSize={15}>
                Gandhinagar, Dombivli (E)
              </Typography>
            </Stack>
            <div style={{ visibility: 'hidden', width: 100 }} />
          </Box>
        )}
        {/* ========== Holy Angels Paradise Kindergarten ========== */}
        {receiptType === 'ReceiptHolyKg' && (
          <Box
            className="Main-div"
            flexDirection="row"
            display="flex"
            mt={5}
            gap={4}
            justifyContent="center"
            alignItems="center"
            p={2}
            mb={2}
            bgcolor="#e2b4bb"
          >
            <img src={holyLogo} width={100} alt="holyLogo" />
            <Stack textAlign="center" flex={1}>
              <Typography variant="h1" fontWeight="bold" fontSize={30} color="primary">
                <b>ANGELS&apos; PARADISE</b>
              </Typography>
              <Typography variant="body1" fontSize={13}>
                The Kindergarten Experience
              </Typography>
              <Typography variant="body1" fontSize={15}>
                Gandhinagar, Dombivli (E)
              </Typography>
            </Stack>
            <div style={{ visibility: 'hidden', width: 100 }} />
          </Box>
        )}
        {/* ========== Holy Angels Paradise College ========== */}
        {receiptType === 'ReceiptHolyCollege' && (
          <Box
            className="Main-div"
            flexDirection="column"
            mt={5}
            bgcolor="yellow"
            border={1}
            p={1}
            display="flex"
            alignItems="center"
            mb={2}
          >
            <img src={holyLogo} width={70} alt="holyLogo" />
            <Stack color={theme.palette.common.black} direction="row" justifyContent="space-between" gap={5}>
              <Typography variant="subtitle2" fontSize={14}>
                Affiliated to CBSE
              </Typography>
              <Typography variant="subtitle2" fontSize={13}>
                TRINITY EDUCATIONAL TRUST&apos;S
              </Typography>
              <Typography variant="subtitle2" fontSize={14}>
                ISO 9001: 2008 Certified
              </Typography>
            </Stack>
            <Typography variant="h1" fontWeight="bold" fontSize={30} color="primary">
              <b>HOLY ANGELS&apos; SCHOOL & Jr. COLLEGE</b>
            </Typography>
            <Typography color={theme.palette.common.black} variant="body1" fontSize={13}>
              (A Private Unaided Minority Institution)
            </Typography>
            <Typography color={theme.palette.common.black} variant="body1" fontSize={9}>
              Behind P & T Colony, Nandivli - Gandhinagar, Dombivli (E) Dist. Thane, MAH
              <EMAIL> Tel: (0251) 2821975, 2821234
            </Typography>
          </Box>
        )}
        {/* ============================ */}
        <Box border={1} p={1} display="flex" justifyContent="space-between" mb={1}>
          <Stack>
            <Typography variant="subtitle1" fontSize={13}>
              Name : <span className="fw-bold">{studentDetails.studentName}</span>
            </Typography>
            <Typography variant="subtitle1" fontSize={13}>
              Admission No : <span className="fw-bold">{studentDetails.admissionNo}</span>
            </Typography>
            <Typography variant="subtitle1" fontSize={13}>
              Class Name : <span className="fw-bold">{studentDetails.className}</span>
            </Typography>
          </Stack>
          <Stack>
            <Typography variant="subtitle1" fontSize={13}>
              Date : <span className="fw-bold">{payDate !== '' ? payDate : formattedDate}</span>
            </Typography>
            <Typography variant="subtitle1" fontSize={13}>
              Receipt No : <span className="fw-bold">{receiptNumber}</span>
            </Typography>
            <Typography variant="subtitle1" fontSize={13}>
              Contact No : <span className="fw-bold">{studentDetails.contactNo}</span>
            </Typography>
          </Stack>
        </Box>
        {/* <Divider sx={{ my: 1 }} /> */}
        <Box
          className="scrollbar"
          sx={{
            height: 'calc(100vh - 305px)',
            overflow: 'auto',
            mb: 2,
            // '&::-webkit-scrollbar': {
            //   width: '0px',
            // },
          }}
        >
          <TableContainer
          // sx={{ minHeight: '250px' }}
          >
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ '&:first-of-type': { paddingLeft: 0 } }}>Fee Title</TableCell>
                  <TableCell>Term Title</TableCell>
                  <TableCell align="right">Amount (INR)</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {feeStatusData?.map((item) => (
                  <TableRow>
                    <TableCell sx={{ '&:first-of-type': { paddingLeft: 0 } }} color="secondary">
                      {item.feeTitle}
                    </TableCell>
                    <TableCell color="secondary"> {item.termTitle}</TableCell>
                    <TableCell align="right"> {item.amount}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <Box sx={{ borderTop: '2px dashed gray' }} py={3} display="flex" justifyContent="space-between">
            <Typography variant="body2" fontWeight={700}>
              Total Amount Paid
            </Typography>
            <Typography variant="body2" fontWeight={700}>
              {totalAmountPaid.toLocaleString()} {/* Display total amount */}
            </Typography>
          </Box>
          <TableContainer sx={{ mb: 2 }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ '&:first-of-type': { paddingLeft: 0 } }}>Payment Mode</TableCell>
                  <TableCell>Reference No</TableCell>
                  <TableCell align="right">Amount</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paymodeData?.map((item) => {
                  const paymentType =
                    paymentTypes.find((m) => m.paymentTypeId === item.paymentTypeId)?.paymentType || '';

                  return (
                    <TableRow>
                      <TableCell sx={{ '&:first-of-type': { paddingLeft: 0 } }} color="secondary">
                        {paymentType}
                      </TableCell>
                      <TableCell> {item.chequeOrDDNo}</TableCell>
                      <TableCell align="right"> {item.amount}</TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
          <Stack className="amount_words" maxWidth={400}>
            <Typography variant="body2" pb={1} color="secondary">
              Total amount in words :&nbsp;
              <span
                className="fw-bold"
                style={{ color: isLight ? theme.palette.common.black : theme.palette.common.white }}
              >
                {grandTotalInWords}
              </span>
            </Typography>
          </Stack>
        </Box>
      </Box>
      <Box sx={{ justifyContent: 'center', position: 'relative' }}>
        <Stack pb={2.5} spacing={2} direction="row" justifyContent="center" sx={{}}>
          <Button
            fullWidth
            variant="contained"
            color="secondary"
            size="medium"
            onClick={() => {
              onClose();
              if (resetAll === true) {
                onCancel();
                setResetAll(false);
              }
            }}
          >
            Cancel
          </Button>
          <LoadingButton
            loadingPosition="start"
            loading={isSubmitting}
            variant="contained"
            fullWidth
            color={!printButtonShow ? 'primary' : 'success'}
            // sx={{ backgroundColor: '#f1f2f3', color: 'black', fontWeight: 600 }}
            size="medium"
            // onClick={handlePrint}
            onClick={!printButtonShow ? handlePay : handlePrint}
          >
            {!printButtonShow ? 'Pay' : 'Print'}
          </LoadingButton>
        </Stack>
      </Box>
      <Stack sx={{ position: 'absolute', top: 10, right: 10 }}>
        <IconButton
          aria-label="close"
          onClick={() => {
            onClose();
            if (resetAll === true) {
              onCancel();
              setResetAll(false);
            }
          }}
          sx={{
            p: 1,
            color: theme.palette.grey[500],
            '&:hover': {},
          }}
        >
          <CloseIcon />
        </IconButton>
      </Stack>
    </Box>
  );
};
