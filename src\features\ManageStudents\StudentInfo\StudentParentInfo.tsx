/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {  TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Avatar,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  useMediaQuery,
  Chip,
  Select,
  MenuItem,
  TablePagination,
  SelectChangeEvent,
} from '@mui/material';
import styled from 'styled-components';
import { GENDER_SELECT } from '@/config/Selection';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import Success from '@/assets/ManageFee/Success.json';
import man from '@/assets/man.png';
import woman from '@/assets/woman.png';
import errorFailedIcon from '@/assets/ManageFee/ErrorIcon.json';
import NoData from '@/assets/no-datas.png';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import BloodtypeIcon from '@mui/icons-material/Bloodtype';
import { BsGenderAmbiguous } from 'react-icons/bs';
import { BiTable } from 'react-icons/bi';
import { TiBusinessCard } from 'react-icons/ti';
import { MdAdd } from 'react-icons/md';
import { Student, StudentInfoData } from '@/config/StudentDetails';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { bluePreset } from '@/utils/Colors';
import DateSelect from '@/components/shared/Selections/DateSelect';
import { StudentListInfo, StudentListRequest } from '@/types/StudentManagement';
import { YearDataType } from '@/types/Dashboard';
import {
  addNewStudent,
  fetchStudentList,
  updateStudent,
  deleteStudent,
} from '@/store/Students/studentManagement.thunks';
import {
  getStudentListData,
  getStudentListPageInfo,
  getStudentListStatus,
  getStudentDeletingRecords,
  getStudentSortColumn,
  getStudentSortDirection,
  getStudentSubmitting,
  getClassData,
  getYearData,
  getYearStatus,
} from '@/config/storeSelectors';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import useAuth from '@/hooks/useAuth';
import { fetchClassList, fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { setSortColumn, setSortDirection } from '@/store/Students/studentManagement.slice';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { ClassListInfo } from '@/types/AcademicManagement';
import dayjs, { Dayjs } from 'dayjs';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import useSettings from '@/hooks/useSettings';
import SortIcon from '@mui/icons-material/Sort';
import DownloadForOfflineIcon from '@mui/icons-material/DownloadForOffline';
import TransgenderIcon from '@mui/icons-material/Transgender';
import LocalPhoneRoundedIcon from '@mui/icons-material/LocalPhoneRounded';
import StudentAddIndividual from './StudentAddIndividual';
import AddStudentMultiple from './AddStudentMultiple';
import { useNavigate } from 'react-router-dom';
import DownloadStudentInfo from './DownloadStudentInfo';

const StudentParentInfoRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    color: white;
    background: linear-gradient(
      0deg,
      rgba(99, 132, 255, 1) 0%,
      rgba(123, 149, 255, 1) 63%,
      rgba(183, 194, 255, 1) 100%
    );
    /* background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]}; */
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    min-height: calc(100vh - 160px);
    @media screen and (max-width: 768px) {
      margin-bottom: 6px;
    }
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;
      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: calc(100vh - 255px);
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
        .MuiTableCell-head:nth-child(1) {
          z-index: 111;
          position: sticky;
          left: 0;
          width: 100px;
        }
        .MuiTableCell-head:nth-child(2) {
          z-index: 111;
          position: sticky;
          left: 100px;
          width: 120px;
        }
        .MuiTableCell-head:nth-child(3) {
          z-index: 111;
          position: sticky;
          left: 220px;
          width: 200px;
        }
        .MuiTableCell-head:last-child {
          z-index: 11;
          position: sticky;
          right: 0px;
          /* width: 50px; */
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
        }
        .MuiTableCell-body:nth-child(1) {
          z-index: 11;
          position: sticky;
          left: 0;
          width: 100px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
        }
        .MuiTableCell-body:nth-child(2) {
          z-index: 11;
          position: sticky;
          left: 100px;
          width: 120px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
        }
        .MuiTableCell-body:nth-child(3) {
          z-index: 11;
          position: sticky;
          left: 220px;
          width: 200px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
        }
        .MuiTableCell-root.MuiTableCell-body:last-child {
          position: sticky;
          right: 0px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
          /* background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[800]}; */
          width: 100px;
          z-index: 1;
        }
      }
      .main_student_card {
        padding-bottom: 30px;
        height: calc(100vh - 285px);
        overflow-y: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }
        @media screen and (max-width: 768px) {
          /* height: 100%; */
          height: calc(100vh - 290px);
        }
      }
    }

    @media screen and (max-width: 991px) {
      .student_table {
        display: none;
      }
    }
    @media screen and (max-width: 991px) {
      .change-view-button {
        display: none;
      }
    }
  }
`;

const DefaultStudentInfo: StudentListInfo = {
  studentId: 0,
  academicId: 0,
  classId: 0,
  studentName: '',
  admissionNumber: '',
  admissionDate: '',
  studentGender: -2,
  studentDOB: '',
  studentLastStudied: '',
  studentBloodGroup: '',
  studentBirthPlace: '',
  studentNationality: '',
  studentMotherTongue: '',
  studentReligion: '',
  studentCaste: '',
  studentPAddress: '',
  studentCAddress: '',
  studentFatherName: '',
  studentFatherQualification: '',
  studentFatherOccupation: '',
  studentFatherNumber: '',
  studentEmailId: '',
  studentMotherName: '',
  studentMotherQualification: '',
  studentMotherOccupation: '',
  studentMotherNumber: '',
  studentImage: '',
  createdBy: 0,
};

function StudentParentInfo() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const adminId: number = user ? user.accountId : 0;
  const [changeView, setChangeView] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const { confirm } = useConfirm();

  // const [forceDispatch, setForceDispatch] = useState(false);
  const [selectedStudentDetail, setSelectedStudentDetail] = useState<StudentListInfo>(DefaultStudentInfo);
  const ClassData = useAppSelector(getClassData);
  const YearData = useAppSelector(getYearData);
  const YearStatus = useAppSelector(getYearStatus);

  const AllClassOption = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };
  const AllYearOption: YearDataType = {
    accademicId: 0,
    accademicTime: 'Select Year',
    accademicStatus: 1,
  };
  const AllGenderOption = {
    id: '-1',
    gender: 'All',
  };
  const defualtYear = YearData[0]?.accademicId || 0;
  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const yearDataWithAllYear = [AllYearOption, ...YearData];
  const gendersDataWithAll = [AllGenderOption, ...GENDER_SELECT];
  const [classFilter, setClassFilter] = useState(classDataWithAllClass[0]);
  const [studentNameFilter, setStudentNameFilter] = useState('');
  const [studentGenderFilter, setStudentGenderFilter] = useState(-1);
  const [admissionNumberFilter, setAdmissionNumberFilter] = useState('');
  const [academicYearFilter, setAcademicYearFilter] = useState(defualtYear);
  const [studentFatherNameFilter, setStudentFatherNameFilter] = useState('');
  const [studentFatherNumberFilter, setStudentFatherNumberFilter] = useState('');
  // const [studentBloodGroupFilter, setStudentBloodGroupFilter] = useState('');
  // const [studentCasteFilter, setStudentCasteFilter] = useState('');
  const [showAddStudents, setShowAddStudents] = React.useState<
    'individual' | 'multiple' | 'studentInfo' | 'downloadInfo'
  >('studentInfo');
  const StudentListStatus = useAppSelector(getStudentListStatus);
  const StudentListData = useAppSelector(getStudentListData);
  // const StudentListError = useAppSelector(getStudentListError);
  const paginationInfo = useAppSelector(getStudentListPageInfo);
  const sortColumn = useAppSelector(getStudentSortColumn);
  const sortDirection = useAppSelector(getStudentSortDirection);
  const isSubmitting = useAppSelector(getStudentSubmitting);
  const deletingRecords = useAppSelector(getStudentDeletingRecords);

  const { pagenumber, pagesize, totalrecords } = paginationInfo;
  const { classId, className } = classFilter || {};
  // const { gender } = studentGenderFilter || {};

  const currentStudentListRequest = useMemo(
    () => ({
      pageNumber: pagenumber,
      pageSize: pagesize,
      sortColumn,
      sortDirection,
      filters: {
        adminId,
        academicId: academicYearFilter,
        classId,
        studentName: studentNameFilter,
        studentGender: studentGenderFilter,
        admissionDate: '',
        admissionNumber: admissionNumberFilter,
        studentDob: '',
        studentFatherName: studentFatherNameFilter,
        studentFatherNumber: studentFatherNumberFilter,
        studentBloodGroup: '',
        studentCaste: '',
      },
    }),
    [
      adminId,
      pagenumber,
      pagesize,
      sortColumn,
      sortDirection,
      classId,
      academicYearFilter,
      studentNameFilter,
      studentGenderFilter,
      admissionNumberFilter,
      studentFatherNameFilter,
      studentFatherNumberFilter,
    ]
  );

  const loadStudentList = useCallback(
    (request: StudentListRequest) => {
      dispatch(fetchStudentList(request));
    },
    [dispatch]
  );

  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === e.target.value);
    if (selectedClass) {
      setClassFilter(selectedClass);
    }
    loadStudentList({
      ...currentStudentListRequest,
      pageNumber: 1,
      filters: { ...currentStudentListRequest.filters, classId: selectedClass ? selectedClass.classId : 0 },
    });
  };
  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(e.target.value, 10));
    loadStudentList({
      ...currentStudentListRequest,
      filters: { ...currentStudentListRequest.filters, academicId: parseInt(e.target.value, 10) },
    });
  };
  const handleGenderChange = (e: SelectChangeEvent) => {
    setStudentGenderFilter(parseInt(GENDER_SELECT.filter((item) => item.id === e.target.value)[0].id, 10));
    loadStudentList({
      ...currentStudentListRequest,
      filters: { ...currentStudentListRequest.filters, studentGender: parseInt(e.target.value, 10) },
    });
  };

  const handleAddNewStudent = () => {
    setSelectedStudentDetail(DefaultStudentInfo);
    setShowAddStudents('individual');
  };

  const handleSaveorEdit = useCallback(
    async (value: StudentListInfo, mode: 'create' | 'edit') => {
      if (mode === 'create') {
        const { studentId, ...rest } = value;
        const response = await dispatch(addNewStudent(rest)).unwrap();

        if (response.id > 0) {
          setShowAddStudents('studentInfo');
          const successMessage = <SuccessMessage message="Student created successfully" />;
          await confirm(successMessage, 'Student Created', { okLabel: 'Ok', showOnlyOk: true });

          loadStudentList({
            ...currentStudentListRequest,
            pageNumber: 1,
            sortColumn: 'studentId',
            sortDirection: 'desc',
          });
        }
      } else {
        const { studentDOB, admissionDate } = value;

        const isDateFormatted = (date: string) => {
          const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
          return dateRegex.test(date);
        };

        // Function to format dates
        const formatDate = (date: Dayjs | string | number | null): string | null => {
          if (date === null) return null;
          if (dayjs.isDayjs(date)) return date.format('DD/MM/YYYY');
          if (typeof date === 'string' && isDateFormatted(date)) return date; // Already formatted
          if (typeof date === 'string' || typeof date === 'number') return dayjs(date).format('DD/MM/YYYY');
          return null; // Fallback for any unexpected types
        };

        // Convert startDate and endDate to DD/MM/YYYY format
        const formattedStudentDOB = formatDate(studentDOB);
        const formattedStudentJoinDate = formatDate(admissionDate);
        const updateReq = {
          ...value,
          studentDOB: formattedStudentDOB,
          admissionDate: formattedStudentJoinDate,
          dbResult: '',
        };
        const response = await dispatch(updateStudent(updateReq)).unwrap();
        if (response.rowsAffected > 0) {
          const successMessage = <SuccessMessage message="Student updated successfully" />;
          await confirm(successMessage, 'Student Updated', { okLabel: 'Ok', showOnlyOk: true });

          loadStudentList(currentStudentListRequest);
        }
      }
    },
    [confirm, currentStudentListRequest, dispatch, loadStudentList]
  );

  useEffect(() => {
    if (YearStatus === 'idle') {
      setAcademicYearFilter(defualtYear);
    }
    if (StudentListStatus === 'idle') {
      loadStudentList(currentStudentListRequest);
      dispatch(fetchYearList(adminId));
      dispatch(fetchClassList(adminId));
    }
    console.log('datass::', StudentListData);
  }, [
    loadStudentList,
    defualtYear,
    YearStatus,
    StudentListStatus,
    currentStudentListRequest,
    StudentListData,
    adminId,
    dispatch,
  ]);

  const getRowKey = useCallback((row: StudentListInfo) => row.studentId, []);

  const handlePageChange = useCallback(
    (event: unknown, newPage: number) => {
      loadStudentList({ ...currentStudentListRequest, pageNumber: newPage + 1 });
    },
    [currentStudentListRequest, loadStudentList]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newPageSize = +event.target.value;
      loadStudentList({ ...currentStudentListRequest, pageNumber: 1, pageSize: newPageSize });
    },
    [currentStudentListRequest, loadStudentList]
  );

  const handleEditStudent = useCallback((studentObj: StudentListInfo) => {
    setSelectedStudentDetail(studentObj);
    console.log(studentObj);
    setShowAddStudents('individual');
  }, []);

  const currentItemCount = StudentListData.length;

  const handleDeleteStudent = useCallback(
    async (studentObj: StudentListInfo) => {
      try {
        const { studentId, studentName } = studentObj;
        const deleteConfirmMessage = (
          <DeleteMessage
            jsonIcon={deleteBin}
            message={
              <div>
                Are you sure you want to delete the Student <br />
                <span style={{ color: theme.palette.error.main }}>&quot;{studentName}&quot;</span> ?
              </div>
            }
          />
        );
        if (await confirm(deleteConfirmMessage, 'Delete Student?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
          const deleteResponse = await dispatch(deleteStudent(studentId)).unwrap();
          if (deleteResponse.deleted) {
            const deleteDoneMessage = (
              <DeleteMessage loop={false} jsonIcon={deleteSuccess} message="Student deleted successfully." />
            );
            await confirm(deleteDoneMessage, 'Deleted', { okLabel: 'Ok', showOnlyOk: true });
            let pageNumberToMove = pagenumber;
            if (paginationInfo.remainingpages === 0 && pagenumber > 1 && currentItemCount === 1) {
              pageNumberToMove = pagenumber - 1;
              loadStudentList({ ...currentStudentListRequest, pageNumber: pageNumberToMove });
            } else {
              loadStudentList(currentStudentListRequest);
            }
          } else {
            const errorMessage = (
              <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Student deleted failed" />
            );
            await confirm(errorMessage, 'Student Delete', { okLabel: 'Ok', showOnlyOk: true });
          }
        }
      } catch {
        const errorMessage = (
          <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Something went wrong please try again." />
        );
        await confirm(errorMessage, 'Student Delete', { okLabel: 'Ok', showOnlyOk: true });
      }
    },
    [
      confirm,
      currentStudentListRequest,
      currentItemCount,
      dispatch,
      loadStudentList,
      pagenumber,
      paginationInfo.remainingpages,
      theme,
    ]
  );

  const handleSort = useCallback(
    (column: string) => {
      const newReq = { ...currentStudentListRequest };
      if (column === sortColumn) {
        console.log('Toggling direction');
        const sortDirectionNew = sortDirection === 'asc' ? 'desc' : 'asc';
        newReq.pageNumber = 1;
        newReq.sortDirection = sortDirectionNew;
        dispatch(setSortDirection(sortDirectionNew));
      } else {
        newReq.pageNumber = 1;
        newReq.sortColumn = column;
        dispatch(setSortColumn(column));
      }

      loadStudentList(newReq);
    },
    [currentStudentListRequest, dispatch, loadStudentList, sortColumn, sortDirection]
  );

  const lgScreenSize = useMediaQuery(theme.breakpoints.down('lg'));
  React.useEffect(() => {
    if (lgScreenSize) {
      setChangeView(true);
    } else {
      setChangeView(false);
    }
  }, [lgScreenSize]);

  const handleToggle = () => {
    setChangeView((prevChangeView) => !prevChangeView);
    setSelectedStudentDetail(DefaultStudentInfo);
  };

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: pagenumber - 1,
      pageSize: pagesize,
      totalRecords: totalrecords,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, pagenumber, pagesize, totalrecords]
  );

  const handleReset = useCallback(
    (e: any) => {
      e.preventDefault();
      setClassFilter(classDataWithAllClass[0]);
      setAcademicYearFilter(defualtYear);
      setStudentNameFilter('');
      setStudentNameFilter('');
      setStudentGenderFilter(-1);
      setAdmissionNumberFilter('');
      setStudentFatherNameFilter('');
      setStudentFatherNumberFilter('');
      loadStudentList({
        pageNumber: 1,
        pageSize: 20,
        sortColumn: 'studentId',
        sortDirection: 'asc',
        filters: {
          adminId,
          academicId: defualtYear,
          classId: -1,
          studentName: '',
          studentGender: -1,
          admissionDate: '',
          admissionNumber: '',
          studentDob: '',
          studentFatherName: '',
          studentFatherNumber: '',
          studentBloodGroup: '',
          studentCaste: '',
        },
      });
    },
    [loadStudentList, classDataWithAllClass, defualtYear, adminId]
  );
  const StudentsParentInfoColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'studentId',
        dataKey: 'studentId',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'admissionNumber',
        dataKey: 'admissionNumber',
        headerLabel: 'Adm No',
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1} alignItems="center">
              <Avatar alt="" src={row.studentImage} />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'class',
        dataKey: 'className',
        headerLabel: 'Class',
      },
      {
        name: 'academicYear',
        dataKey: 'academicTime',
        headerLabel: 'Year',
      },
      {
        name: 'gender',
        dataKey: 'studentGender',
        headerLabel: 'Gender',
        renderCell: (row) => {
          return (
            <Typography variant="subtitle1" fontSize={14}>
              {row.studentGender === 0 ? 'Male' : row.studentGender === 1 ? 'Female' : 'Other'}
            </Typography>
          );
        },
      },
      // {
      //   name: 'bloodGroup',
      //   dataKey: 'studentBloodGroup',
      //   headerLabel: 'Blood',
      // },
      // {
      //   name: 'DOB',
      //   dataKey: 'studentDob',
      //   headerLabel: 'DOB',
      // },
      {
        name: 'studentFatherName',
        dataKey: 'studentFatherName',
        headerLabel: 'Parent',
      },
      {
        name: 'studentFatherNumber',
        dataKey: 'studentFatherNumber',
        headerLabel: 'Phone No',
      },

      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton onClick={() => handleEditStudent(row)} size="small" sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton onClick={() => handleDeleteStudent(row)} size="small" color="error" sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    [handleEditStudent, handleDeleteStudent]
  );
  return showAddStudents === 'downloadInfo' ? (
    <DownloadStudentInfo onBackClick={() => setShowAddStudents('studentInfo')} />
  ) : showAddStudents === 'multiple' ? (
    <AddStudentMultiple studentDetail={DefaultStudentInfo} onBackClick={() => setShowAddStudents('studentInfo')} />
  ) : showAddStudents === 'individual' ? (
    <StudentAddIndividual
      onBackClick={() => setShowAddStudents('studentInfo')}
      studentDetail={selectedStudentDetail}
      onSave={handleSaveorEdit}
      onCancel={() => setShowAddStudents('studentInfo')}
      onClose={() => setShowAddStudents('studentInfo')}
    />
  ) : (
    <Page title="Student Info">
      <StudentParentInfoRoot>
        <Card className="Card" elevation={1}>
          <Stack
            direction="row"
            flexWrap="wrap"
            justifyContent="space-between"
            alignItems="center"
            sx={{ px: { xs: 3, md: 5 }, pt: { xs: 2, md: 3 }, pb: 1 }}
          >
            <Stack direction="row" alignItems="center" justifyContent="space-between" flex={3} whiteSpace="nowrap">
              <Typography variant="h6" fontSize={17}>
                Student Info
              </Typography>
              <div>
                {changeView && (
                  <Tooltip title="Sort Order">
                    <IconButton aria-label="sort" color="primary" sx={{ mr: 2 }} onClick={() => handleSort('')}>
                      <SortIcon />
                    </IconButton>
                  </Tooltip>
                )}
                <Tooltip title="Search">
                  <IconButton
                    aria-label="search"
                    color="primary"
                    sx={{ mr: 2 }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              </div>
            </Stack>
            <Box
              display="flex"
              justifyContent="end"
              alignItems="center"
              columnGap={2}
              sx={{
                flexShrink: 0,
                flex: 1,
                '@media (max-width: 340px)': {
                  flexWrap: 'wrap',
                  rowGap: 1,
                },
              }}
            >
              <Button
                startIcon={<MdAdd />}
                sx={{
                  borderRadius: '20px',
                  whiteSpace: 'nowrap',
                  '@media (max-width: 340px)': {
                    width: '100%',
                  },
                }}
                size="small"
                variant="outlined"
                onClick={() => setShowAddStudents('multiple')}
              >
                Add Multiple
              </Button>
              <Button
                startIcon={<MdAdd />}
                onClick={handleAddNewStudent}
                sx={{
                  borderRadius: '20px',
                  whiteSpace: 'nowrap',
                  '@media (max-width: 340px)': {
                    width: '100%',
                  },
                }}
                size="small"
                variant="outlined"
              >
                Enroll Indivitual
              </Button>
              <Button
                startIcon={<DownloadForOfflineIcon />}
                onClick={() => {
                  setShowAddStudents('downloadInfo');
                  navigate('download-student-info');
                }}
                sx={{
                  borderRadius: '20px',
                  whiteSpace: 'nowrap',
                  '@media (max-width: 340px)': {
                    width: '100%',
                  },
                }}
                size="small"
                color="success"
                variant="contained"
              >
                Download
              </Button>
              <Tooltip title={changeView === true ? 'Table View' : 'Card View'}>
                <IconButton className="change-view-button" color="primary" onClick={handleToggle}>
                  {changeView === true ? <BiTable /> : <TiBusinessCard />}
                </IconButton>
              </Tooltip>
            </Box>
          </Stack>
          <Divider />

          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={2} sx={{ px: { xs: 3, md: 5 } }}>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student Name
                      </Typography>
                      <TextField
                        fullWidth
                        placeholder="Enter Name"
                        name="studentName"
                        value={studentNameFilter}
                        onChange={(e) => {
                          setStudentNameFilter(e.target.value);
                          loadStudentList({
                            ...currentStudentListRequest,
                            filters: { ...currentStudentListRequest.filters, studentName: e.target.value },
                          });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Admission No.
                      </Typography>
                      <TextField
                        fullWidth
                        placeholder="Enter No."
                        name="admissionNumber"
                        value={admissionNumberFilter}
                        onChange={(e) => {
                          setAdmissionNumberFilter(e.target.value);
                          loadStudentList({
                            ...currentStudentListRequest,
                            filters: { ...currentStudentListRequest.filters, admissionNumber: e.target.value },
                          });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilterSelect"
                        value={className}
                        onChange={handleClassChange}
                        placeholder="Select"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '250px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        {classDataWithAllClass?.map((item: ClassListInfo) => (
                          <MenuItem key={item.classId} value={item.className}>
                            {item.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Gender
                      </Typography>
                      <Select
                        labelId="genderFilterSelect"
                        id="genderFilterSelect"
                        value={studentGenderFilter.toString()}
                        onChange={handleGenderChange}
                        placeholder="Select"
                      >
                        {GENDER_SELECT.map((item) => (
                          <MenuItem key={item.id} value={item.id}>
                            {item.gender}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Father Name
                      </Typography>
                      <TextField
                        fullWidth
                        placeholder="Enter Name"
                        name="studentFatherName"
                        value={studentFatherNameFilter}
                        onChange={(e) => {
                          setStudentFatherNameFilter(e.target.value);
                          loadStudentList({
                            ...currentStudentListRequest,
                            filters: { ...currentStudentListRequest.filters, studentFatherName: e.target.value },
                          });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Father Number
                      </Typography>
                      <TextField
                        type="number"
                        fullWidth
                        placeholder="Enter Number"
                        name="studentFatherNumber"
                        value={studentFatherNumberFilter}
                        onChange={(e) => {
                          setStudentFatherNumberFilter(e.target.value);
                          loadStudentList({
                            ...currentStudentListRequest,
                            filters: { ...currentStudentListRequest.filters, studentFatherNumber: e.target.value },
                          });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth onClick={handleReset}>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box
            // bgcolor={changeView === true ? (isLight ? theme.palette.primary.lighter : theme.palette.grey[800]) : ''}
            >
              {changeView === true ? (
                <Box display="flex" flexDirection="column" className="">
                  {StudentListData.length !== 0 ? (
                    <Box
                      className="main_student_card"
                      sx={{
                        overflow: 'hidden',
                        px: { xs: 3, md: 5 },
                        py: { xs: 2, md: 3 },
                      }}
                    >
                      <Grid container spacing={5}>
                        {StudentListData.map((student, rowIndex) => {
                          const rowkey = getRowKey(student, rowIndex);
                          let isDeleting = false;
                          if (rowkey && deletingRecords && deletingRecords.length > 0) {
                            isDeleting = deletingRecords.includes(rowkey);
                          }
                          return (
                            <Grid key={rowkey} item xxl={3} xl={4} lg={4} md={6} sm={6} xs={12}>
                              <Card
                                className="student_card"
                                sx={{
                                  opacity: isDeleting ? 0.5 : 1,
                                  boxShadow: 5,
                                  p: 2,
                                  borderRadius: 4,
                                  // border: 1,
                                  // borderColor: theme.palette.grey[300],
                                }}
                              >
                                <Box display="flex" flexDirection="column" textAlign="center" gap={1}>
                                  <Stack direction="row" justifyContent="space-between" alignItems="center" gap={1}>
                                    <Chip
                                      size="small"
                                      label={`AD : ${student.admissionNumber}`}
                                      sx={{
                                        border: '0px',
                                        fontWeight: 600,
                                        width: 'fit-content',
                                        // backgroundColor: theme.palette.primary.lighter,
                                        backgroundColor: '#8992fb ',
                                        // color: theme.palette.primary.main,
                                        color: 'white',
                                      }}
                                    />
                                    <Stack>
                                      <MenuEditDelete
                                        color="#fff"
                                        Edit={() => handleEditStudent(student)}
                                        Delete={() => handleDeleteStudent(student)}
                                      />
                                    </Stack>
                                  </Stack>

                                  <Stack direction="row" justifyContent="center" gap={2} alignItems="center">
                                    <Box
                                      // border={2}
                                      borderRadius={10}
                                      p={1.5}
                                      sx={{
                                        position: 'relative',
                                        overflow: 'hidden',
                                        '&::before': {
                                          content: '""',
                                          position: 'absolute',
                                          top: 0,
                                          left: 0,
                                          right: 0,
                                          bottom: 0,
                                          borderRadius: 'inherit',
                                          padding: '1.3px', // Adjust to control border thickness
                                          background: 'linear-gradient(to top, #7691ff, #fff)', // Your gradient here
                                          WebkitMask:
                                            'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                                          WebkitMaskComposite: 'xor',
                                          maskComposite: 'exclude',
                                        },
                                      }}
                                    >
                                      <Avatar
                                        sx={{
                                          width: 100,
                                          height: 100,
                                          // boxShadow: '0px 0px 15px #fff '
                                        }}
                                        alt="Remy Sharp"
                                        // src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?auto=format&fit=crop&q=80&w=1780&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                                        src={
                                          student.studentImage ?? student.studentGender === 0
                                            ? man
                                            : student.studentGender === 1
                                            ? woman
                                            : ''
                                        }
                                      />
                                    </Box>
                                  </Stack>
                                  <Stack direction="column" alignItems="center">
                                    <Typography variant="subtitle2" fontSize={17}>
                                      {student.studentName}
                                    </Typography>
                                    <Typography variant="subtitle1" fontSize={13}>
                                      <span>
                                        <b>{student.className}</b>
                                      </span>
                                      <span>
                                        &nbsp;&nbsp;
                                        <b>&nbsp;&nbsp;{student.academicTime}</b>
                                      </span>
                                    </Typography>
                                  </Stack>
                                  <Stack direction="column" gap={1} alignItems="center">
                                    <Typography variant="subtitle1" fontSize={13}>
                                      <span>
                                        <TransgenderIcon
                                          fontSize="small"
                                          style={{
                                            color: '#6283ff ',
                                            backgroundColor: '#fff',
                                            borderRadius: 10,
                                            padding: 3,
                                          }}
                                        />
                                        {/* <MaleIcon color="info" fontSize="small" /> */}
                                        <b>
                                          &nbsp;:&nbsp;
                                          {student.studentGender === 0
                                            ? 'Male'
                                            : student.studentGender === 1
                                            ? 'Female'
                                            : 'Other'}
                                        </b>
                                      </span>
                                      &nbsp;&nbsp;
                                      <span>
                                        <BloodtypeIcon
                                          fontSize="small"
                                          style={{
                                            color: '#6283ff ',
                                            backgroundColor: '#fff',
                                            borderRadius: 10,
                                            padding: 1,
                                          }}
                                        />
                                        <b>&nbsp;:&nbsp;{student.studentBloodGroup}</b>
                                      </span>
                                    </Typography>
                                    <Typography variant="subtitle1" fontSize={13}>
                                      <span>
                                        <b>
                                          <span className="">D.O.B</span>&nbsp;:&nbsp;
                                          {dayjs(student.studentDOB, 'YYYY/MM/DD').format('DD/MM/YYYY')}
                                        </b>
                                      </span>
                                    </Typography>
                                    <Typography variant="subtitle1" fontSize={13}>
                                      <span>
                                        <b>
                                          <span className=""> Parent</span>&nbsp;:&nbsp;
                                          {student.studentFatherName}
                                        </b>
                                      </span>
                                    </Typography>
                                    <Chip
                                      icon={
                                        <LocalPhoneRoundedIcon
                                          fontSize="small"
                                          style={{
                                            color: '#fff ',
                                            backgroundColor: '#6283ff',
                                            borderRadius: 10,
                                            padding: 3,
                                          }}
                                        />
                                      }
                                      size="small"
                                      label={student.studentFatherNumber}
                                      // variant="contained"
                                      sx={{
                                        border: '0px',
                                        fontWeight: 600,
                                        width: 'fit-content',
                                        // backgroundColor: bluePreset.lighter,
                                        backgroundColor: '#fff',
                                        color: '#6283ff',
                                        // color: '#fff',
                                      }}
                                    />
                                  </Stack>
                                </Box>
                              </Card>
                            </Grid>
                          );
                        })}
                      </Grid>
                    </Box>
                  ) : (
                    <Box
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                      width="100%"
                      height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 284px)' }}
                    >
                      <Stack direction="column" alignItems="center">
                        <img src={NoData} width="150px" alt="" />
                        <Typography variant="subtitle2" mt={2} color="GrayText">
                          No data found !
                        </Typography>
                      </Stack>
                    </Box>
                  )}
                  <TablePagination
                    rowsPerPageOptions={pageProps.rowsPerPageOptions}
                    component="div"
                    count={pageProps.totalRecords}
                    rowsPerPage={pageProps.pageSize}
                    page={pageProps.pageNumber}
                    onPageChange={pageProps.onPageChange}
                    onRowsPerPageChange={pageProps.onRowsPerPageChange}
                    showFirstButton
                    showLastButton
                    // sx={{
                    //   paddingRight: '30px',
                    //   borderEndEndRadius: 20,
                    //   borderBottomLeftRadius: 20,
                    //   boxShadow: 0,
                    //   backgroundColor: isLight ? theme.palette.common.white : theme.palette.grey[800],
                    //   position: 'fixed',
                    //   bottom: { xs: '0px', md: '16px' },
                    //   right: { xs: '16px', md: '16px' },
                    //   left: { xs: '16px', md: `calc(${SIDE_BAR_WIDTH} + 16px)` },
                    // }}
                  />
                </Box>
              ) : (
                <Box className="student_table" sx={{ px: { xs: 3, md: 5 }, pb: { xs: 2, md: 2.5 } }}>
                  <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
                    <DataTable
                      tableStyles={{ minWidth: { xs: '1300px' } }}
                      showHorizontalScroll
                      columns={StudentsParentInfoColumns}
                      data={StudentListData}
                      getRowKey={getRowKey}
                      fetchStatus="success"
                      allowPagination
                      allowSorting
                      sortColumn={sortColumn}
                      sortDirection={sortDirection}
                      onSort={handleSort}
                      PaginationProps={pageProps}
                      deletingRecords={deletingRecords}
                    />
                  </Paper>
                </Box>
              )}
            </Box>
          </div>
        </Card>
      </StudentParentInfoRoot>
    </Page>
  );
}

export default StudentParentInfo;
