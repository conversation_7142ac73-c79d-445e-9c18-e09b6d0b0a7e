/* eslint-disable no-prototype-builtins */
/* eslint-disable no-else-return */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Card,
  Avatar,
  IconButton,
  Collapse,
  Select,
  MenuItem,
  FormControl,
  useTheme,
  SelectChangeEvent,
  Tooltip,
  Radio,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import SaveIcon from '@mui/icons-material/Save';
import Success from '@/assets/ManageFee/Success.json';
import Updated from '@/assets/ManageFee/Updated.json';
import UpdateLoading from '@/assets/ManageFee/UpdateLoading.json';
import SaveLoading from '@/assets/ManageFee/SaveLoading.json';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import errorIcon from '@/assets/ManageFee/Error.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import studentSuccessIcon from '@/assets/ManageFee/studentSuccessJsonIcon.json';
import studentSelectedIcon from '@/assets/ManageFee/studentSelectedIcon.json';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import useSettings from '@/hooks/useSettings';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getClassSectionsData,
  getManageFeeclassListData,
  getScholarshipSettingsListData,
  getScholarshipSettingsListStatus,
  getYearData,
} from '@/config/storeSelectors';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import {
  DeleteAllScholarshipMapped,
  DeleteScholarshipMapped,
  createScholarshipSettings,
  fetchClassList,
  fetchClassSections,
  fetchScholarshipSettings,
} from '@/store/ManageFee/manageFee.thunks';
import {
  GetScholarshipDataType,
  GetStudentsMappedDataType,
  ScholarshipMappedDeleteAllType,
} from '@/types/ManageFee';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import Lottie from 'lottie-react';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import LoadingButton from '@mui/lab/LoadingButton';
import PositionedSnackbar from '@/components/shared/Popup/SnackBar';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import NoData from '@/assets/no-datas.png';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';

const ScholarshipSettingRoot = styled.div`
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        /* border: 1px solid ${(props) => props.theme.palette.grey[200]}; */
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }

        .MuiTableCell-root {
          border: 1px solid ${(props) => props.theme.palette.grey[100]};
        }
        .MuiTableCell-head {
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.light : props.theme.palette.grey[900]};
        }
        .MuiTableCell-head:nth-child(1) {
          z-index: 11;
          position: sticky;
          left: 0;
          width: 50px;
        }
        .MuiTableCell-head:nth-child(2) {
          z-index: 11;
          position: sticky;
          left: 52.5px;
          width: 120px;
        }
        .MuiTableCell-head:nth-child(3) {
          z-index: 11;
          position: sticky;
          left: 164px;
          width: 200px;
        }
        .MuiTableCell-head:last-child {
          z-index: 11;
          position: sticky;
          right: 0px;
          width: 80px;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(1) {
          position: sticky;
          left: 0;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
          width: 50px;
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(2) {
          position: sticky;
          left: 52.5px;
          padding-left: 5px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
          width: 100px;
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(3) {
          position: sticky;
          left: 164px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
          width: 200px;
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:last-child {
          position: sticky;
          right: 0px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[800]};
          width: 80px;
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body {
          padding: 0px;
          height: 100%;
        }

        .MuiTableCell-root:first-child {
          padding-left: 10px;
        }
      }
    }
  }
`;

export default function ScholarshipSetting({ onBackClick }: any) {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const adminId: number = user ? user.accountId : 0;
  const academicId = 10;
  const [showFilter, setShowFilter] = useState(true);

  const YearData = useAppSelector(getYearData);
  const [classSectionsFilter, setClassSectionsFilter] = useState(0);
  const [classFilter, setClassFilter] = useState(0);
  const ClassSectionsData = useAppSelector(getClassSectionsData);
  const classListData = useAppSelector(getManageFeeclassListData);
  // const classListStatus = useAppSelector(getclassListStatus);

  const [academicYearFilter, setAcademicYearFilter] = useState(0);
  const [feeTypeFilter, setFeeTypeFilter] = useState(0);

  const scholarshipSettingsListData = useAppSelector(getScholarshipSettingsListData);
  const scholarshipSettingsListStatus = useAppSelector(getScholarshipSettingsListStatus);

  const [scholarshipData, setScholarshipData] = useState<any>([]);
  // const [scholarshipData, setScholarshipData] = useState<GetScholarshipSettingsDataType[]>([]);
  const [studentsData, setStudentsData] = useState<GetStudentsMappedDataType[]>([]);
  const [selectedCells, setSelectedCells] = useState<{ studentId: number; scholarshipId: number }[]>([]);
  const [saving, setSaving] = useState(false);
  const [savingAll, setSavingAll] = useState(false);
  const [individualSaveLoading, setIndividualSaveLoading] = useState<Record<string, boolean>>({});
  const [succesResponse, setSuccesResponse] = useState('');
  const [showSuccessIcon, setShowSuccessIcon] = useState<{ [key: string]: boolean }>({});
  const [individualSaveButtonEnabled, setIndividualSaveButtonEnabled] = useState<{ [studentId: number]: string[] }>([]);
  const [showErrorIcon, setShowErrorIcon] = useState<{ [key: string]: boolean }>({});
  const [rowId, setRowId] = useState({});
  const [saveAllButtonDisabled, setSaveAllButtonDisabled] = useState<boolean>(true);
  const [snackBar, setSnackBar] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<GetStudentsMappedDataType[]>([]);

  const initialScholarshipSettingsRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      sectionId: 0,
      classId: 0,
      feeTypeId: feeTypeFilter,
    }),
    [adminId, academicYearFilter, feeTypeFilter]
  );
  const currentScholarshipSettingsRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      sectionId: classSectionsFilter,
      classId: classFilter,
      feeTypeId: feeTypeFilter,
    }),
    [adminId, academicYearFilter, classSectionsFilter, classFilter, feeTypeFilter]
  );

  const loadScholarshipList = useCallback(
    async (request: { adminId: number; academicId: number; sectionId: number; classId: number; feeTypeId: number }) => {
      try {
        setSelectedCells([]);
        const data: any = await dispatch(fetchScholarshipSettings(request)).unwrap();
        console.log('data::::', data);
        if (data) {
          const studentsMap = data.studentsMapped
            ? data.studentsMapped.map((item: GetStudentsMappedDataType) => ({ ...item }))
            : [];
          setStudentsData(studentsMap);
          console.log('studentsMap::::', studentsMap);
        }
      } catch (error) {
        console.error('Error loading Optional fee list:', error);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    // if (optionalFeeSettingListStatus === 'idle') {
    // loadScholarshipList(currentScholarshipSettingsRequest);
    dispatch(fetchYearList(adminId));
    dispatch(fetchClassList({ adminId, academicId, sectionId: classSectionsFilter }));
    dispatch(fetchClassSections({ adminId, academicId }));
    // }
    setScholarshipData(scholarshipSettingsListData);
    console.log('scholarshipData::::----', scholarshipData);
    console.log('selectedCells::::----', selectedCells);
    console.log('rowId::::----', rowId);
    console.log('individualSaveButtonEnabled::::----', individualSaveButtonEnabled);
  }, [
    dispatch,
    adminId,
    classSectionsFilter,
    selectedCells,
    scholarshipData,
    scholarshipSettingsListData,
    academicYearFilter,
    rowId,
    individualSaveButtonEnabled,
  ]);
  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadScholarshipList({ ...currentScholarshipSettingsRequest, academicId: parseInt(e.target.value, 10) });
    // setClassFilter(0);
    setSelectedCells([]);
  };

  const handleFeeTypeChange = (e: SelectChangeEvent) => {
    const feeTypeId = parseInt(e.target.value, 10);
    setFeeTypeFilter(feeTypeId);
    loadScholarshipList({ ...currentScholarshipSettingsRequest, feeTypeId: parseInt(e.target.value, 10) });
  };

  const handleClassSectionChange = (e: SelectChangeEvent) => {
    setClassSectionsFilter(
      parseInt(ClassSectionsData.filter((item) => item.sectionId === e.target.value)[0].sectionId, 10)
    );
    loadScholarshipList({ ...currentScholarshipSettingsRequest, sectionId: parseInt(e.target.value, 10) });
    setClassFilter(0);
    setSelectedCells([]);
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    setClassFilter(parseInt(classListData.filter((item) => item.classId === e.target.value)[0].classId, 10));
    loadScholarshipList({ ...currentScholarshipSettingsRequest, classId: parseInt(e.target.value, 10) });
    setSelectedCells([]);
  };

  const handleSave = useCallback(
    async (row: GetStudentsMappedDataType) => {
      try {
        setSaving(true);
        // const scholarshipId = row.scholarshipId;
        const { studentId } = row;
        console.log('row::::----', row);

        setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.studentId]: true }));

        // Construct feeAmount array based on section amounts
        const scholarshipMapArray = Object.keys(rowId)
          .filter((key) => parseInt(key.split('_')[0], 10) === studentId)
          // Filter objects where scholarshipId matches row.scholarshipId
          .map((key) => ({
            scholarshipId: parseInt(key.split('_')[1], 10),
            dbResult: 'string',
            scholarshipStudentMapId: 0,
          }));

        // const sendReq: CreateScholarshipSettingsDataType = [
        const sendReq: any = [
          {
            adminId,
            accademicId: academicYearFilter,
            academicscholarshipId: 0,
            sectionId: classSectionsFilter,
            classId: classFilter,
            studentId,
            feeTypeId: feeTypeFilter,
            scholarshipMap: scholarshipMapArray,
          },
        ];
        const actionResult = await dispatch(createScholarshipSettings(sendReq));

        if (actionResult && Array.isArray(actionResult.payload)) {
          const results = actionResult.payload;
          console.log('results::::----', results);

          const filteredResults = results.filter((f) => f.scholarshipMap);
          console.log('filteredResults::::----', filteredResults);

          const scholarshipMapArr = filteredResults.map((f) => f.scholarshipMap);

          const scholarshipArr = scholarshipMapArr.flat();
          console.log('scholarshipArr::::----', scholarshipArr);

          // Find the item with dbResult === 'Success'
          const SuccessResult = scholarshipArr.find((f) => f.dbResult === 'Success');
          console.log('SuccessResult::::----', SuccessResult);
          if (SuccessResult) {
            setSuccesResponse(SuccessResult.dbResult);
          }

          // Find the item with dbResult === 'Updated'
          const UpdateResult = scholarshipArr.find((f) => f.dbResult === 'Updated');
          console.log('UpdateResult::::----', UpdateResult);
          if (UpdateResult) {
            setSuccesResponse(UpdateResult.dbResult);
          }
          setSaving(false);
          // setTermFeeDetails(response);
          setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.studentId]: false }));
          setIndividualSaveButtonEnabled((prevEnabled) => {
            const updatedEnabled = { ...prevEnabled };
            if (updatedEnabled[studentId]) {
              delete updatedEnabled[studentId]; // Remove studentId key if it exists
            }
            return updatedEnabled;
          });

          // // Update termFeeMapped for the current row
          // const updatedscholarshipMapData = optionalFeeData.map((item) => {
          //   if (item.studentId === studentId) {
          //     // Push each item of optionalFeeArr separately to feeMapped
          //     const updatedfeeMapped = [...item.feeMapped, ...optionalFeeArr];
          //     return { ...item, feeMapped: updatedfeeMapped };
          //   }
          //   return item;
          // });
          // setScholarshipData(updatedOptionalFeeData);
          // console.log('updatedOptionalFeeData::::----', updatedOptionalFeeData);

          // Update checkedRows when the row is unchecked
          // const updatedCheckedRows = { ...checkedRows };
          // if (checkedRows[row.scholarshipId]) {
          //   delete updatedCheckedRows[row.scholarshipId];
          // }
          // setCheckedRows(updatedCheckedRows);

          loadScholarshipList({
            ...currentScholarshipSettingsRequest,
            academicId: academicYearFilter,
            adminId,
            sectionId: classSectionsFilter,
            classId: classFilter,
          });
          setShowSuccessIcon((prevMap) => ({ ...prevMap, [row.studentId]: true }));
          setTimeout(() => {
            setShowSuccessIcon((prevMap) => ({ ...prevMap, [row.studentId]: false }));
          }, 5000);
        }
      } catch (error) {
        // Handle errors here
        // await showConfirmation(<ErrorMessage icon={ErrorMsg} message="Message content already created" />, '');
      }
    },
    [
      dispatch,
      adminId,
      academicYearFilter,
      classSectionsFilter,
      classFilter,
      rowId,
      currentScholarshipSettingsRequest,
      loadScholarshipList,
      feeTypeFilter,
    ]
  );
  const handleAllSave = useCallback(async () => {
    try {
      setSavingAll(true);
      // const scholarshipId = row.scholarshipId;

      const studentIdArray = Object.keys(rowId).map((key) => parseInt(key.split('_')[0], 10));

      // Ensure scholarshipIdArray contains unique values
      const uniquestudentIds = [...new Set(studentIdArray)];
      await Promise.all(
        uniquestudentIds.map(async (studentId) => {
          // Construct feeAmount array based on section amounts
          const scholarshipMapArray = Object.keys(rowId)
            .filter((key) => parseInt(key.split('_')[0], 10) === studentId) // Filter objects where scholarshipId matches row.scholarshipId
            .map((key) => ({
              scholarshipId: parseInt(key.split('_')[1], 10),
              dbResult: 'string',
              scholarshipStudentMapId: 0,
            }));

          const sendReq: any = [
            {
              adminId,
              accademicId: academicYearFilter,
              academicscholarshipId: 0,
              sectionId: classSectionsFilter,
              classId: classFilter,
              studentId,
              feeTypeId: feeTypeFilter,
              scholarshipMap: scholarshipMapArray,
            },
          ];
          const actionResult = await dispatch(createScholarshipSettings(sendReq));

          if (actionResult && Array.isArray(actionResult.payload)) {
            setSnackBar(true);
            const results = actionResult.payload;
            console.log('results::::----', results);
            // Filter out items without termAmount property
            const filteredResults = results.filter((f) => f.scholarshipMap);
            console.log('filteredResults::::----', filteredResults);

            // Extract termAmount arrays
            const scholarshipMapArr = filteredResults.map((f) => f.scholarshipMap);

            // Flatten the array of termAmount arrays
            const scholarshipArr = scholarshipMapArr.flat();
            console.log('scholarshipArr::::----', scholarshipArr);

            // Find the item with dbResult === 'Success'
            const SuccessResult = scholarshipArr.find((f) => f.dbResult === 'Success');
            console.log('SuccessResult::::----', SuccessResult);
            if (SuccessResult) {
              setSuccesResponse(SuccessResult.dbResult);
            }

            // Find the item with dbResult === 'Updated'
            const UpdateResult = scholarshipArr.find((f) => f.dbResult === 'Updated');
            console.log('UpdateResult::::----', UpdateResult);
            if (UpdateResult) {
              setSuccesResponse(UpdateResult.dbResult);
            }
            setSavingAll(false);
            // setTermFeeDetails(response);
            setIndividualSaveButtonEnabled([]);
            setSaveAllButtonDisabled(true);
            // // Update termFeeMapped for the current row
            // const updatedOptionalFeeData = optionalFeeData.map((item) => {
            //   if (item.studentId === studentId) {
            //     // Push each item of optionalFeeArr separately to feeMapped
            //     const updatedfeeMapped = [...item.feeMapped, ...optionalFeeArr];
            //     return { ...item, feeMapped: updatedfeeMapped };
            //   }
            //   return item;
            // });
            // setScholarshipData(updatedOptionalFeeData);
            // console.log('updatedOptionalFeeData::::----', updatedOptionalFeeData);

            // Update checkedRows when the row is unchecked
            // const updatedCheckedRows = { ...checkedRows };
            // if (checkedRows[row.scholarshipId]) {
            //   delete updatedCheckedRows[row.scholarshipId];
            // }
            // setCheckedRows(updatedCheckedRows);

            loadScholarshipList({
              ...currentScholarshipSettingsRequest,
              academicId: academicYearFilter,
              adminId,
              sectionId: classSectionsFilter,
              classId: classFilter,
            });
          }
        })
      );
    } catch (error) {
      // Handle errors here
      // await showConfirmation(<ErrorMessage icon={ErrorMsg} message="Message content already created" />, '');
    }
  }, [
    dispatch,
    adminId,
    academicYearFilter,
    classSectionsFilter,
    classFilter,
    rowId,
    currentScholarshipSettingsRequest,
    loadScholarshipList,
    feeTypeFilter,
  ]);
  // Function to handle cell selection
  const handleCellClick = useCallback(
    async (studentId: number, scholarshipId: number) => {
      const cellKey = `${studentId}_${scholarshipId}`;

      // Check if cell is already selected
      const cellIndex = selectedCells.findIndex(
        (cell) => cell.studentId === studentId && cell.scholarshipId === scholarshipId
      );

      if (cellIndex !== -1) {
        // Cell already selected, deselect it
        setSelectedCells((prevSelectedCells) =>
          prevSelectedCells.filter((cell) => !(cell.studentId === studentId && cell.scholarshipId === scholarshipId))
        );
        setRowId((prev) => {
          const updatedIds: Record<string, number> = { ...prev };
          delete updatedIds[cellKey];
          return updatedIds;
        });
        setIndividualSaveButtonEnabled((prev) => {
          const updatedEnabled = { ...prev };
          if (updatedEnabled[studentId]) {
            updatedEnabled[studentId] = updatedEnabled[studentId].filter((key) => key !== cellKey);
            if (updatedEnabled[studentId].length === 0) {
              delete updatedEnabled[studentId]; // Remove studentId array if it becomes empty
            }
          }
          if (updatedEnabled) {
            const isSaveAllButtonDisabled = Object.keys(updatedEnabled).length === 0;
            setSaveAllButtonDisabled(isSaveAllButtonDisabled);
          }
          return updatedEnabled;
        });
      } else {
        setSelectedCells((prevSelectedCells) => [...prevSelectedCells, { studentId, scholarshipId }]);
        // Cell not selected, select it
        setRowId((prev) => {
          const updatedIds: Record<string, number> = { ...prev };
          updatedIds[cellKey] = Number(cellKey);
          return updatedIds;
        });
        setIndividualSaveButtonEnabled((prevEnabled) => {
          const updatedEnabled = { ...prevEnabled };
          if (!updatedEnabled[studentId]) {
            updatedEnabled[studentId] = []; // Initialize as an empty array if not exists
          }
          if (!updatedEnabled[studentId].includes(cellKey)) {
            updatedEnabled[studentId].push(cellKey); // Add cellKey to the array
            if (updatedEnabled) {
              const isSaveAllButtonDisabled = Object.keys(updatedEnabled).length === 0;
              setSaveAllButtonDisabled(isSaveAllButtonDisabled);
            }
          }
          return updatedEnabled;
        });
        // Enable the Save All button
      }
      // Check if any cells are selected
    },
    [setSelectedCells, selectedCells]
  );

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      loadScholarshipList(initialScholarshipSettingsRequest);
      setAcademicYearFilter(0);
      setClassSectionsFilter(0);
      setClassFilter(0);
      setFeeTypeFilter(0);
      // dispatch(fetchFeeDateSettings({ adminId, academicId: 10, sectionId: 0 }));
    },
    [initialScholarshipSettingsRequest, loadScholarshipList]
  );

  const handleDeleteCell = useCallback(
    async (scholarshipStudentMapId: number) => {
      const sendConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div style={{ color: theme.palette.error.main }}>
              Are you sure you want to delete the cell &quot;{}&quot; ?
            </div>
          }
        />
      );

      if (await confirm(sendConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [
          {
            adminId,
            accademicId: academicYearFilter,
            scholarshipStudentMapId,
            dbResult: '',
            deletedId: 0,
          },
        ];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(DeleteScholarshipMapped(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Deleted');

          if (!errorMessages) {
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
            loadScholarshipList(currentScholarshipSettingsRequest);
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          loadScholarshipList(currentScholarshipSettingsRequest);
        }
      }
    },
    [confirm, dispatch, adminId, academicYearFilter, theme, loadScholarshipList, currentScholarshipSettingsRequest]
  );

  const handleDeleteRow = useCallback(
    async (row: GetStudentsMappedDataType) => {
      const { studentId } = row;
      console.log('row::::----', row);
      // const fineMappedIds = row.filter((f) => f.termId === term.termId).map((m) => m.fineMappedId);

      const sendConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div style={{ color: theme.palette.error.main }}>
              Are you sure you want to delete the row &quot;{}&quot; ?
            </div>
          }
        />
      );

      if (await confirm(sendConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [
          {
            adminId,
            accademicId: academicYearFilter,
            classId: classFilter,
            studentId,
            dbResult: 'string',
            deletedId: 0,
          },
        ];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(DeleteAllScholarshipMapped(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');

          if (!errorMessages) {
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
            loadScholarshipList(currentScholarshipSettingsRequest);
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          loadScholarshipList(currentScholarshipSettingsRequest);
        }
      }
    },
    [
      confirm,
      dispatch,
      adminId,
      academicYearFilter,
      classFilter,
      theme,
      loadScholarshipList,
      currentScholarshipSettingsRequest,
    ]
  );

  const handleDeleteMultiple = useCallback(async () => {
    const sendConfirmMessage = (
      <DeleteMessage jsonIcon={deleteBin} message={<div>Are you sure you want to delete all ?</div>} />
    );
    if (await confirm(sendConfirmMessage, 'Delete Basic Fee?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
      // const deleteResponse = await dispatch(deleteBasicFeeList(sendReq));
      const sendRequests: ScholarshipMappedDeleteAllType[] = await Promise.all(
        selectedRows?.map(async (row) => {
          const sendReq = {
            adminId,
            accademicId: academicYearFilter,
            classId: classFilter,
            studentId: row.studentId,
            dbResult: '',
            deletedId: 0,
          };
          return sendReq;
        }) || []
      );
      const deleteResponse = await dispatch(DeleteAllScholarshipMapped(sendRequests));

      console.log('deleteResponse', deleteResponse);
      if (deleteResponse && Array.isArray(deleteResponse.payload)) {
        const results: ScholarshipMappedDeleteAllType[] = deleteResponse.payload;
        const errorMessages = results.find((result) => result.dbResult === 'Failed');
        const successMessages = results.find((result) => result.dbResult === 'Success');

        if (!errorMessages) {
          const deleteSuccessMessage = (
            <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete All Successfully" />
          );
          await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          loadScholarshipList(currentScholarshipSettingsRequest);
        } else if (!successMessages) {
          const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete Failed" />;
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const deleteErrorMessage = (
            <DeleteMessage loop={false} jsonIcon={errorIcon} message="Something went wrong please try again" />
          );
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        }
        loadScholarshipList(currentScholarshipSettingsRequest);
        // setSelectedRows([]);
        // setTermFeeDetails((prevDetails) => prevDetails.filter((item) => !selectedRows.includes(item)));
      }
    }
  }, [
    confirm,
    dispatch,
    adminId,
    selectedRows,
    academicYearFilter,
    classFilter,
    loadScholarshipList,
    currentScholarshipSettingsRequest,
  ]);

  const ScholarshipSettingListColumns: DataTableColumn<GetStudentsMappedDataType>[] = useMemo(() => {
    const baseColumns = [
      {
        name: 'slNo',
        renderHeader: () => (
          <Typography variant="subtitle2" fontSize={13}>
            Admission No
          </Typography>
        ),
        headerLabel: '',
        renderCell: (row: GetStudentsMappedDataType) => {
          return (
            <Typography variant="subtitle1" fontSize={13}>
              {row.admissionNo}
            </Typography>
          );
        },
        sortable: true,
      },
      {
        name: 'studentName',
        renderHeader: () => (
          <Typography variant="subtitle2" fontSize={13}>
            Student Name
          </Typography>
        ),
        renderCell: (row: GetStudentsMappedDataType) => {
          return (
            <Stack pl={1} direction="row" alignItems="center" gap={1}>
              <Avatar src="" />
              <Typography variant="subtitle2" fontSize={13}>
                {row.studentName}
              </Typography>
            </Stack>
          );
        },
      },

      ...(scholarshipData.scholarship
        ? scholarshipData.scholarship.map((item: GetScholarshipDataType) => ({
            name: `${item.scholarshipId}`,
            renderHeader: () => (
              <Stack className="header-color" direction="row" justifyContent="center">
                <Typography variant="subtitle2" fontSize={12}>
                  {item.title}
                </Typography>
              </Stack>
            ),
            renderCell: (row: GetStudentsMappedDataType) => {
              const studentIds = row.scholarshipMapped
                ? row.scholarshipMapped.map((i: GetStudentsMappedDataType) => i.studentId)
                : [];
              const scholarshipIds = row.scholarshipMapped
                ? row.scholarshipMapped.map((i: { scholarshipId: number }) => i.scholarshipId)
                : [];
              const scholarshipStudentMapId = row.scholarshipMapped?.find(
                (i: { scholarshipId: number; studentId: number }) =>
                  i.scholarshipId === item.scholarshipId && i.studentId === row.studentId
              )?.scholarshipStudentMapId;
              const isSavedCellStudent =
                studentIds.includes(row.studentId) && scholarshipIds.includes(item.scholarshipId);
              return (
                <Button sx={{ borderRadius:0, p: 0, width: '100%', height: '100%' }} color="info">
                  <Stack
                    onClick={() => {
                      if (!isSavedCellStudent) handleCellClick(row.studentId, item.scholarshipId);
                    }}
                    sx={{
                      position: 'relative',
                      background: isSavedCellStudent
                        ? theme.palette.success.lighter
                        : selectedCells.some(
                            (cell) => cell.studentId === row.studentId && cell.scholarshipId === item.scholarshipId
                          ) // Check if this cell was clicked
                        ? theme.palette.info.lighter
                        : '',
                      '&:hover': {
                        background: !isSavedCellStudent&&!selectedCells.some(
                          (cell) => cell.studentId === row.studentId && cell.scholarshipId === item.scholarshipId
                        )
                          ? theme.palette.info.lighter
                          : '',
                        transition: '0.5s',
                      },
                    }}
                    width="100%"
                    direction="row"
                    justifyContent="center"
                    // gap={2}
                    // py={1}
                  >
                    {selectedCells.some(
                      (cell) => cell.studentId === row.studentId && cell.scholarshipId === item.scholarshipId
                    ) && (
                      <Radio
                        checked={selectedCells.some(
                          (cell) => cell.studentId === row.studentId && cell.scholarshipId === item.scholarshipId
                        )}
                        // Check if this cell is selected
                        // onChange={() => handleCellClick(row.studentId, item.scholarshipId)} // Handle cell click
                        size="small"
                        checkedIcon={<SuccessIcon sx={{ color: theme.palette.info.main }} />}
                        sx={{ position: 'absolute', right: 0, top: 7 }}
                        disableRipple
                      />
                    )}

                    {isSavedCellStudent ? (
                      <>
                        <Lottie animationData={studentSuccessIcon} loop={false} style={{ width: '50px', m: 0 }} />
                        <IconButton
                          onClick={() => handleDeleteCell(scholarshipStudentMapId)}
                          size="small"
                          sx={{ position: 'absolute', top: 1, right: 1 }}
                        >
                          <DeleteIcon fontSize="small" color="secondary" sx={{ width: 15, height: 15 }} />
                        </IconButton>
                      </>
                    ) : !selectedCells.some(
                        (cell) => cell.studentId === row.studentId && cell.scholarshipId === item.scholarshipId
                      ) ? (
                      <Stack
                        py={!isSavedCellStudent ? 2.2 : ''}
                        width="100%"
                        direction="row"
                        alignItems="center"
                        justifyContent="center"
                        position="relative"
                      >
                        <PersonAddIcon
                          sx={{
                            color: theme.palette.grey[600],
                          }}
                        />
                      </Stack>
                    ) : (
                      <Stack width="100%" direction="row" alignItems="center" justifyContent="center">
                        <Lottie animationData={studentSelectedIcon} loop={false} style={{ width: '50px', m: 0 }} />
                      </Stack>
                    )}
                  </Stack>
                </Button>
              );
            },
          }))
        : []),

      {
        name: '',
        renderHeader: () => (
          <Typography textAlign="start" className="header-color" variant="subtitle2" fontSize={14}>
            Actions
          </Typography>
        ),
        renderCell: (row: GetStudentsMappedDataType) => {
          return (
            <Stack width={80} direction="row" alignItems="center" justifyContent="center" gap={1}>
              {showSuccessIcon[row.studentId] === true ? (
                <Stack direction="row" justifyContent="center" flexDirection="column" alignItems="center">
                  {succesResponse === 'Success' ? (
                    <>
                      <Lottie animationData={Success} loop={false} style={{ width: '30px' }} />
                      <Typography color={theme.palette.success.main} fontSize={7} variant="subtitle2">
                        Saved
                      </Typography>
                    </>
                  ) : (
                    <>
                      <Lottie animationData={Updated} loop={false} style={{ width: '30px' }} />
                      <Typography color={theme.palette.warning.main} fontSize={7} variant="subtitle2">
                        Updated
                      </Typography>
                    </>
                  )}
                </Stack>
              ) : showErrorIcon[row.studentId] === true ? (
                <Stack direction="row" justifyContent="center">
                  <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
                </Stack>
              ) : !individualSaveLoading[row.studentId] === true ? (
                <IconButton
                  disabled={!individualSaveButtonEnabled.hasOwnProperty(row.studentId) || false}
                  size="small"
                  color="success"
                  aria-label=""
                  onClick={() => handleSave(row)}
                >
                  <SaveIcon fontSize="small" />
                </IconButton>
              ) : (
                <Stack direction="row" justifyContent="center" flexDirection="column" alignItems="center">
                  <>
                    <Lottie animationData={SaveLoading} loop style={{ width: '20px' }} />
                    <Typography color={theme.palette.success.main} fontSize={7} variant="subtitle2">
                      Saving...
                    </Typography>
                  </>
                </Stack>
              )}

              <IconButton
                disabled={row.scholarshipMapped === null}
                onClick={() => handleDeleteRow(row)}
                size="small"
                color="error"
                aria-label=""
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Stack>
          );
        },
      },
    ];

    return [...baseColumns];
  }, [
    theme,
    handleCellClick,
    selectedCells,
    scholarshipData,
    individualSaveButtonEnabled,
    showErrorIcon,
    handleSave,
    showSuccessIcon,
    individualSaveLoading,
    succesResponse,
    handleDeleteCell,
    handleDeleteRow,
  ]);

  const getRowKey = useCallback(
    (row: GetStudentsMappedDataType, index?: number) => `${index}_${row.studentId ?? ''}`,
    []
  );
  return (
    <Page title="Fees Collection">
      <ScholarshipSettingRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Box flexWrap="wrap" display="flex" justifyContent="space-between" alignItems="center">
            <Stack gap={2} direction="row" alignItems="center">
              <Tooltip title="Back">
                <IconButton
                  onClick={onBackClick}
                  sx={{
                    backgroundColor: theme.palette.secondary.main,
                    color: theme.palette.common.white,
                    '&:hover': { backgroundColor: theme.palette.secondary.dark },
                    width: '25px',
                    height: '25px',
                  }}
                  size="small"
                >
                  <ArrowBackIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Typography variant="h6" fontSize={17}>
                Scholarship Setting
              </Typography>
            </Stack>

            <Box pb={1} sx={{ flexShrink: 0 }}>
              {selectedRows.length > 0 && (
                <Tooltip title="Delete">
                  <IconButton
                    sx={{ visibility: { xs: 'hidden', sm: 'visible' } }}
                    aria-label="delete"
                    color="error"
                    onClick={handleDeleteMultiple}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              )}
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ mr: { xs: 0, sm: 1 } }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton
                  aria-label="delete"
                  color="primary"
                  sx={{ mr: { xs: 0, sm: 1 } }}
                  onClick={() => setShowFilter((x) => !x)}
                >
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Box>
          </Box>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={4} container spacing={2} alignItems="end">
                  <Grid item xxl={2} xl={2.5} lg={3} md={3} sm={3.3} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={3} sm={3.3} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Type
                      </Typography>
                      <Select
                        labelId="feeTypeFilter"
                        id="feeTypeFilterSelect"
                        value={feeTypeFilter.toString()}
                        onChange={handleFeeTypeChange}
                        placeholder="Select Year"
                      >
                        <MenuItem sx={{ display: 'none' }} value={0}>
                          Select Type
                        </MenuItem>
                        {FEE_TYPE_ID_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={3} sm={3.3} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Section
                      </Typography>
                      <Select
                        labelId="classSectionsFilter"
                        id="classSectionsFilterSelect"
                        value={classSectionsFilter?.toString()}
                        onChange={handleClassSectionChange}
                        placeholder="Select Section"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Section
                        </MenuItem>
                        {ClassSectionsData.map((opt) => (
                          <MenuItem key={opt.sectionId} value={opt.sectionId}>
                            {opt.sectionName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={3} sm={3.3} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        disabled={classListData.length === 0}
                        labelId="classFilter"
                        id="classFilter"
                        value={classFilter?.toString()}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            backgroundColor: theme.palette.grey[200],
                          },
                        }}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Class
                        </MenuItem>
                        {classListData.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  {/* <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Date
                      </Typography>
                      <DatePickers name="messageDateFilter" value="" />
                    </FormControl>
                  </Grid> */}

                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      {/* <Button type="submit" variant="contained" color="primary" fullWidth>
                        Search
                      </Button> */}
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            {studentsData.length !== 0 && classFilter !== 0 && (
              <Box pr={1} mt={!showFilter ? 2 : 0} display="flex" justifyContent="end">
                <LoadingButton
                  loading={savingAll}
                  loadingPosition="start"
                  onClick={handleAllSave}
                  disabled={saveAllButtonDisabled}
                  color="success"
                  startIcon={<SaveIcon fontSize="small" />}
                  size="small"
                  variant="contained"
                  sx={{ py: 0.2, px: 1, fontSize: 12 }}
                >
                  {/* {switchToUpdateButton ? 'Update All ' : 'Save All'} */}
                  {savingAll ? 'Saving All' : 'Save All'}
                </LoadingButton>
              </Box>
            )}
            {studentsData.length !== 0 && classFilter !== 0 ? (
              <Paper
                className="card-table-container"
                sx={{
                  marginTop: '12px',
                  border: scholarshipData.length !== 0 ? `2px solid ${theme.palette.grey[200]} ` : '',
                }}
              >
                <DataTable
                  tableStyles={{ minWidth: { xs: '1000px' } }}
                  showHorizontalScroll
                  ShowCheckBox
                  setSelectedRows={setSelectedRows}
                  selectedRows={selectedRows}
                  columns={ScholarshipSettingListColumns}
                  data={studentsData}
                  getRowKey={getRowKey}
                  // fetchStatus={scholarshipSettingsListStatus}
                  fetchStatus="success"
                />
              </Paper>
            ) : (
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                width="100%"
                height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 400px)' }}
              >
                <Stack direction="column" alignItems="center">
                  <img src={NoData} width="150px" alt="" />
                  <Typography variant="subtitle2" mt={2} color="GrayText">
                    No data found !
                  </Typography>
                </Stack>
              </Box>
            )}
          </div>
        </Card>
      </ScholarshipSettingRoot>
      <PositionedSnackbar
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        content="Save All Successfully"
        open={snackBar}
        TransitionComponent="SlideTransition"
        onClose={() => setSnackBar(false)}
      />
    </Page>
  );
}
