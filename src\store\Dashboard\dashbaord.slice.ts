import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import {
  DashboardFeeChartType,
  DashboardEventsType,
  DashboardState,
  DashboardStatsType,
  DashboardTimeTableType,
  DashboardBdayType,
  DashboardAttendanceType,
  DashboardVideosType,
} from '@/types/Dashboard';
import { ClassListInfo } from '@/types/AcademicManagement';
import {
  fetchClassList,
  fetchDashboardAttendance,
  fetchDashboardBday,
  fetchDashboardEvents,
  fetchDashboardFee<PERSON>hart,
  fetchDashboardStats,
  fetchDashboardTimeTable,
  fetchDashboardVideos,
  fetchYearList,
} from './dashboard.thunks';
import { flushStore } from '../flush.slice';

const initialState: DashboardState = {
  dashboardStats: {
    data: [],
    status: 'idle',
    error: null,
  },
  dashboardEvents: {
    data: [],
    status: 'idle',
    error: null,
  },
  dashboardBday: {
    data: [],
    status: 'idle',
    error: null,
  },
  dashboardFeeChart: {
    data: [],
    status: 'idle',
    error: null,
  },
  dashboardTimetable: {
    data: [],
    status: 'idle',
    error: null,
  },
  dashboardAttendance: {
    data: [],
    status: 'idle',
    error: null,
  },
  dashboardVideos: {
    data: [],
    status: 'idle',
    error: null,
  },
  classList: {
    data: [],
    status: 'idle',
    error: null,
  },
  yearList: {
    data: [],
    status: 'idle',
    error: null,
  },
};

export const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    setDashboardStats: (state, action: PayloadAction<DashboardStatsType[]>) => {
      state.dashboardStats.data = action.payload; // Update the 'data' property of dashboardStats
    },
    setEvents: (state, action: PayloadAction<DashboardEventsType[]>) => {
      state.dashboardEvents.data = action.payload; // Update the 'data' property of events
    },
    setTimeTable: (state, action: PayloadAction<DashboardTimeTableType[]>) => {
      state.dashboardTimetable.data = action.payload; // Update the 'data' property of timetable
    },
    setBday: (state, action: PayloadAction<DashboardBdayType[]>) => {
      state.dashboardBday.data = action.payload; // Update the 'data' property of Bday
    },
    setFeeChart: (state, action: PayloadAction<DashboardFeeChartType[]>) => {
      state.dashboardFeeChart.data = action.payload; // Update the 'data' property of FeeChart
    },
    setAttendance: (state, action: PayloadAction<DashboardAttendanceType[]>) => {
      state.dashboardAttendance.data = action.payload; // Update the 'data' property of Attendance
    },
    setVideos: (state, action: PayloadAction<DashboardVideosType[]>) => {
      state.dashboardVideos.data = action.payload; // Update the 'data' property of Videos
    },
    setClassList: (state, action: PayloadAction<ClassListInfo[]>) => {
      state.classList.data = action.payload; // Update the 'data' property of ClassList
    },
  },
  extraReducers(builder) {
    builder
      .addCase(fetchDashboardStats.pending, (state) => {
        state.dashboardStats.status = 'loading';
        state.dashboardStats.error = null;
      })
      .addCase(fetchDashboardStats.fulfilled, (state, action) => {
        state.dashboardStats.status = 'success';
        state.dashboardStats.data = action.payload;
        state.dashboardStats.error = null;
      })
      .addCase(fetchDashboardStats.rejected, (state) => {
        state.dashboardStats.status = 'error';
        state.dashboardStats.error = 'Unknown error in fetching Dashboard stats';
      })

      // extraReducers for fetching events

      .addCase(fetchDashboardEvents.pending, (state) => {
        state.dashboardEvents.status = 'loading';
        state.dashboardEvents.error = null;
      })
      .addCase(fetchDashboardEvents.fulfilled, (state, action) => {
        state.dashboardEvents.status = 'success';
        state.dashboardEvents.data = action.payload;
        state.dashboardEvents.error = null;
      })
      .addCase(fetchDashboardEvents.rejected, (state, action) => {
        state.dashboardEvents.status = 'error';
        state.dashboardEvents.error = action.payload || 'Unknown error in fetching Dashboard events';
      })

      // extraReducers for fetching Bday

      .addCase(fetchDashboardBday.pending, (state) => {
        state.dashboardBday.status = 'loading';
        state.dashboardBday.error = null;
      })
      .addCase(fetchDashboardBday.fulfilled, (state, action) => {
        state.dashboardBday.status = 'success';
        state.dashboardBday.data = action.payload;
        state.dashboardBday.error = null;
      })
      .addCase(fetchDashboardBday.rejected, (state, action) => {
        state.dashboardBday.status = 'error';
        state.dashboardBday.error = action.payload || 'Unknown error in fetching Dashboard Bday';
      })

      // extraReducers for fetching FeeChart

      .addCase(fetchDashboardFeeChart.pending, (state) => {
        state.dashboardFeeChart.status = 'loading';
        state.dashboardFeeChart.error = null;
      })
      .addCase(fetchDashboardFeeChart.fulfilled, (state, action) => {
        state.dashboardFeeChart.status = 'success';
        state.dashboardFeeChart.data = action.payload;
        state.dashboardFeeChart.error = null;
      })
      .addCase(fetchDashboardFeeChart.rejected, (state, action) => {
        state.dashboardFeeChart.status = 'error';
        state.dashboardFeeChart.error = action.payload || 'Unknown error in fetching Dashboard FeeChart';
      })

      // extraReducers for fetching TimeTable

      .addCase(fetchDashboardTimeTable.pending, (state) => {
        state.dashboardTimetable.status = 'loading';
        state.dashboardTimetable.error = null;
      })
      .addCase(fetchDashboardTimeTable.fulfilled, (state, action) => {
        state.dashboardTimetable.status = 'success';
        state.dashboardTimetable.data = action.payload;
        state.dashboardTimetable.error = null;
      })
      .addCase(fetchDashboardTimeTable.rejected, (state, action) => {
        state.dashboardTimetable.status = 'error';
        state.dashboardTimetable.error = action.payload || 'Unknown error in fetching Dashboard timetable';
      })
      // extraReducers for fetching Attendance

      .addCase(fetchDashboardAttendance.pending, (state) => {
        state.dashboardAttendance.status = 'loading';
        state.dashboardAttendance.error = null;
      })
      .addCase(fetchDashboardAttendance.fulfilled, (state, action) => {
        state.dashboardAttendance.status = 'success';
        state.dashboardAttendance.data = action.payload;
        state.dashboardAttendance.error = null;
      })
      .addCase(fetchDashboardAttendance.rejected, (state, action) => {
        state.dashboardAttendance.status = 'error';
        state.dashboardAttendance.error = action.payload || 'Unknown error in fetching Dashboard Attendance';
      })
      // extraReducers for fetching Videos

      .addCase(fetchDashboardVideos.pending, (state) => {
        state.dashboardVideos.status = 'loading';
        state.dashboardVideos.error = null;
      })
      .addCase(fetchDashboardVideos.fulfilled, (state, action) => {
        state.dashboardVideos.status = 'success';
        state.dashboardVideos.data = action.payload;
        state.dashboardVideos.error = null;
      })
      .addCase(fetchDashboardVideos.rejected, (state, action) => {
        state.dashboardVideos.status = 'error';
        state.dashboardVideos.error = action.payload || 'Unknown error in fetching Dashboard Videos';
      })

      // extraReducers for fetching ClassList

      .addCase(fetchClassList.pending, (state) => {
        state.classList.status = 'loading';
        state.classList.error = null;
      })
      .addCase(fetchClassList.fulfilled, (state, action) => {
        state.classList.status = 'success';
        state.classList.data = action.payload;
        state.classList.error = null;
      })
      .addCase(fetchClassList.rejected, (state, action) => {
        state.classList.status = 'error';
        state.classList.error = action.payload || 'Unknown error in fetching Class List';
      })

      // extraReducers for fetching YearList

      .addCase(fetchYearList.pending, (state) => {
        state.yearList.status = 'loading';
        state.yearList.error = null;
      })
      .addCase(fetchYearList.fulfilled, (state, action) => {
        state.yearList.status = 'success';
        state.yearList.data = action.payload;
        state.yearList.error = null;
      })
      .addCase(fetchYearList.rejected, (state, action) => {
        state.yearList.status = 'error';
        state.yearList.error = action.payload || 'Unknown error in fetching Year List';
      })
      .addCase(flushStore, () => initialState);
  },
});

export const { setDashboardStats, setEvents, setTimeTable } = dashboardSlice.actions;

export default dashboardSlice.reducer;
