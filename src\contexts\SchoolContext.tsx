// context/SchoolContext.tsx
import React, { createContext, useContext, useState, useMemo } from 'react';
import passdailLogo from '@/assets/SchoolLogos/logo-small.svg';

export interface School {
  schoolId: string;
  schoolName: string;
  code: string;
  schoolLogo: any;
  infoCardData: any;
}

interface SchoolContextType {
  selectedSchool: School | null;
  setSelectedSchool: (school: School) => void;
}

const SchoolContext = createContext<SchoolContextType | undefined>(undefined);

export const SchoolProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [selectedSchool, setSelectedSchool] = useState<School | null>({
    schoolId: '1',
    schoolName: 'Passdaily',
    code: 'SS',
    schoolLogo: passdailLogo,
    infoCardData: [
      {
        id: 1,
        title: 'Passdaily',
        content: 'For students seeking admissions from Nursery, Jr. kg & Sr. kg.',
      },
      {
        id: 2,
        title: 'Passdaily School',
        content: 'For students seeking admissions from standard 1st to standard 10.',
      },
      {
        id: 3,
        title: 'Passdaily College',
        content: 'Passdaily Junior College to Class XI Science & Commerce.',
      },
    ],
  });

  const contextValue = useMemo(() => ({ selectedSchool, setSelectedSchool }), [selectedSchool]);

  return <SchoolContext.Provider value={contextValue}>{children}</SchoolContext.Provider>;
};

export const useSchool = (): SchoolContextType => {
  const context = useContext(SchoolContext);
  if (!context) throw new Error('useSchool must be used within a SchoolProvider');
  return context;
};
