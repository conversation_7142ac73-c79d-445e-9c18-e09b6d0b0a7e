/* eslint-disable no-nested-ternary */
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  Typography,
  TableHead,
  TableRow,
  Button,
  Stack,
  useTheme,
  IconButton,
  TextField,
  Skeleton,
  Tooltip,
  Card,
} from '@mui/material';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import PrintIcon from '@mui/icons-material/Print';
import CloseIcon from '@mui/icons-material/Close';
import errorIcon from '@/assets/ManageFee/Error.json';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import { useReactToPrint } from 'react-to-print';
import PictureAsPdfRoundedIcon from '@mui/icons-material/PictureAsPdfRounded';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { ReceiptCancel, fetchReceiptForPrint } from '@/store/ManageFee/manageFee.thunks';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import LoadingButton from '@mui/lab/LoadingButton';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getReceiptForPrintListData, getReceiptForPrintListStatus } from '@/config/storeSelectors';
import dayjs from 'dayjs';
import generatePDF, { Resolution, Margin, Options } from 'react-to-pdf';
import useAuth from '@/hooks/useAuth';
import { GetReceiptDetailsType, GetReceiptPaymentModeType, GetReceiptTermfeepaidType } from '@/types/ManageFee';
import passdailLogo from '@/assets/SchoolLogos/logo-small.svg';
import holyLogo from '@/assets/SchoolLogos/HolyLogo.jpeg';
import carmelLogo from '@/assets/SchoolLogos/CarmelLogo.png';
import thereseLogo from '@/assets/SchoolLogos/StthereseLogo.png';
import thomasLogo from '@/assets/SchoolLogos/StThomasLogo.png';
import MIMLogo from '@/assets/SchoolLogos/MIMLogo.png';
import nirmalaLogo from '@/assets/SchoolLogos/NirmalaLogo.png';
import useSettings from '@/hooks/useSettings';
import styled from 'styled-components';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import Page from './Page';

const ReceiptPDFRoot = styled.div`
  /* padding: 1rem; */
  /* width: 1000px; */
  /* height: calc(100vh - 20px); */
  /* background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[900]}; */
  @media screen and (max-width: 768px) {
    width: 1000px;
  }
  display: flex;
  flex-direction: column;
  /* position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1111; */
  .Card {
    /* min-height: 140vh; */
    overflow: scroll;
    @media screen and (max-width: 576px) {
      height: 100%;
    }
  }
  .print-preview-container {
    top: 0px;
    bottom: 0px;
    margin: 20px;
    padding: 20px;
    border: 1px solid;
  }
`;

type ReaceiptPrps = {
  receiptIdNo?: number;
  onClose?: () => void;
};
export const ReceiptPDF = ({ receiptIdNo, onClose }: ReaceiptPrps) => {
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const { user } = useAuth();
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const adminId: number | undefined = user?.accountId;
  const receiptForPrintListData = useAppSelector(getReceiptForPrintListData);
  const receiptForPrintListStatus = useAppSelector(getReceiptForPrintListStatus);
  const [paymodeData, setPaymodeData] = React.useState<GetReceiptPaymentModeType[]>([]);
  const [termfeepaid, setTermfeepaid] = React.useState<GetReceiptTermfeepaidType[]>([]);
  const [receiptDetails, setReceiptDetails] = React.useState<GetReceiptDetailsType | undefined>(undefined);
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);
  const {
    schoolName,
    schoolAddress,
    schoolEmail,
    schoolPhone,
    studentName,
    createdDate,
    grandTotal,
    receiptId,
    receiptType,
    className,
    contactNo,
    admissionNo,
    grandTotalWords,
    receiptNo,
  } = receiptDetails || {};

  // const handlePrint = useReactToPrint({
  //   content: () => componentRef.current,
  // });
  const formattedDate = dayjs(createdDate, 'YYYY/MM/DD').format('DD/MM/YYYY');
  const totalAmountPaid = termfeepaid?.reduce((total, item) => total + item.totalAmount, 0);

  const options: Options = useMemo(
    () => ({
      resolution: Resolution.MEDIUM,
      filename: `Receipt-${receiptNo}.pdf`,
      page: {
        margin: Margin.SMALL,
        format: 'letter',
      },
      canvas: {
        mimeType: 'image/png',
        qualityRatio: 1,
      },
      overrides: {
        pdf: {
          compress: true,
        },
        canvas: {
          useCORS: true,
          // removeContainer: true,
        },
      },
    }),
    [receiptNo]
  );

  const getTargetElement = () => document.getElementById('content-id');

  const currentReceiptListRequest = React.useMemo(
    () => ({
      adminId,
      receiptId: receiptIdNo,
    }),
    [adminId, receiptIdNo]
  );

  const loadReceiptDataList = useCallback(
    async (request: { adminId: number | undefined; receiptId: number | undefined }) => {
      try {
        const data = await dispatch(fetchReceiptForPrint(request)).unwrap();
        console.log('data::::', data);
        setReceiptDetails(data.receiptDetail);
        setPaymodeData(data.paymentmode);
        setTermfeepaid(data.termfeepaid);
      } catch (error) {
        console.error('Error loading term fee list:', error);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    loadReceiptDataList(currentReceiptListRequest);
  }, [loadReceiptDataList, currentReceiptListRequest]);

  const handleDownloadPDF = useCallback(async () => {
    setLoading(true);
    try {
      await generatePDF(getTargetElement, options);
    } finally {
      setLoading(false);
    }
  }, [options]);
  // const pdfRef = useRef();

  // const downloadPDF = () => {
  //   const input = pdfRef.current;
  //   html2canvas(input).then((canvas) => {
  //     const imgData = canvas.toDataURL('image/png');
  //     const pdf = new jsPDF('p', 'mm', 'a4', true);
  //     const pdfWidth = pdf.internal.pageSize.getWidth();
  //     const pdfHeight = pdf.internal.pageSize.getHeight();
  //     const imgWidth = canvas.width;
  //     const imgHeight = canvas.height;
  //     const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
  //     const imgX = (pdfWidth - imgWidth * ratio) / 2;
  //     const imgY = 30;
  //     pdf.addImage(imgData, 'PNG', imgX, imgY, imgWidth * ratio, imgHeight * ratio);
  //     pdf.save('invoice.pdf');
  //   });
  // };

  return (
    // <Page title="">
    <ReceiptPDFRoot>
      <Box sx={{ px: { xs: 3, md: 3 } }}>
        <Box
          // position="absolute"
          // bgcolor={isLight ? theme.palette.common.white : theme.palette.common.black}
          // zIndex={11}
          display="flex"
          justifyContent="space-between"
          alignItems="start"
        >
          <Stack onClick={onClose} direction="row" gap={1} alignItems="center" sx={{ cursor: 'pointer' }}>
            <Tooltip title="Back">
              <IconButton
                sx={{
                  backgroundColor: theme.palette.secondary.main,
                  color: theme.palette.common.white,
                  '&:hover': { backgroundColor: theme.palette.secondary.dark },
                  width: '25px',
                  height: '25px',
                }}
                size="small"
              >
                <ArrowBackIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Typography variant="subtitle2">Back</Typography>
          </Stack>
          <Stack pb={2.5} spacing={2} direction="row" justifyContent="end" sx={{}}>
            {/* <Button variant="contained" color="secondary" size="medium" onClick={onClose}>
            Cancel
          </Button> */}
            <LoadingButton
              loadingPosition="start"
              startIcon={<PictureAsPdfRoundedIcon />}
              loading={loading}
              variant="contained"
              color="info"
              size="small"
              onClick={handleDownloadPDF}
              // onClick={downloadPDF}
            >
              Download
            </LoadingButton>
          </Stack>
        </Box>

        <Box py="1%" id="content-id">
          <Box border={1} p={3} className="print-preview-container">
            {receiptForPrintListStatus === 'loading' ? (
              <Box flexDirection="column" display="flex" gap={1} alignItems="center" mb={receiptType ? 2 : 2}>
                <Skeleton variant="circular" width={70} height={70} />
                <Skeleton variant="rounded" width={180} height={15} />
                <Skeleton variant="rounded" width={230} height={15} />
                <Skeleton variant="rounded" width={300} height={15} />
              </Box>
            ) : (
              <Box flexDirection="column" display="flex" alignItems="center" mb={receiptType ? 2 : 0}>
                <img src={passdailLogo} width={70} alt="passdailLogo" />
                {/* {receiptType === 'ReceiptDefault' && <img src={holyLogo} width={70} alt="holyLogo" />} */}
                {/* <img src={carmelLogo} width={70} alt="carmelLogo" /> */}
                {/* <img src={thereseLogo} width={70} alt="thereseLogo" /> */}
                {/* <img src={thomasLogo} width={70} alt="thomasLogo" /> */}
                {/* <img src={nirmalaLogo} width={70} alt="nirmalaLogo" /> */}
                {/* <img src={MIMLogo} width={70} alt="MIMLogo" /> */}
                {receiptType === 'ReceiptDefault' && (
                  <>
                    <Typography variant="subtitle2" fontSize={16} color="primary">
                      {schoolName}
                    </Typography>
                    <Typography variant="body1" fontSize={13} color="secondary">
                      {schoolAddress}
                    </Typography>
                    <Typography variant="body1" fontSize={13} color="secondary">
                      {schoolEmail}, {schoolPhone}
                    </Typography>
                  </>
                )}
              </Box>
            )}
            {/* ============ MIM HIGH SCHOOL =========== */}
            {/* <Typography variant="subtitle2" fontSize={16} color="primary">
            Mueenul Islam Manoor High School
          </Typography>
          <Typography variant="body1" fontSize={13} color="secondary">
            KANDANAKAM,KALADI PO MALAPPURAM 679582
          </Typography>
          <Typography variant="body1" fontSize={13} color="secondary">
            04942103095,9645942121
          </Typography> */}
            {/* ========== Holy Angels Gardens ========== */}
            {receiptType === 'ReceiptHolyNursery' && (
              <Box
                className="Main-div"
                flexDirection="row"
                display="flex"
                mt={5}
                gap={4}
                justifyContent="center"
                alignItems="center"
                p={2}
                mb={2}
                bgcolor="#e2b4bb"
              >
                <img src={holyLogo} width={100} alt="holyLogo" />
                <Stack textAlign="center" flex={1}>
                  <Typography variant="h1" fontWeight="bold" fontSize={30} color="primary">
                    <b>ANGELS&apos; GARDENS</b>
                  </Typography>
                  <Typography variant="body1" fontSize={13}>
                    The Kindergarten Experience
                  </Typography>
                  <Typography variant="body1" fontSize={15}>
                    Gandhinagar, Dombivli (E)
                  </Typography>
                </Stack>
                <div style={{ visibility: 'hidden', width: 100 }} />
              </Box>
            )}
            {/* ========== Holy Angels Paradise Kindergarten ========== */}
            {receiptType === 'ReceiptHolyKg' && (
              <Box
                className="Main-div"
                flexDirection="row"
                display="flex"
                mt={5}
                gap={4}
                justifyContent="center"
                alignItems="center"
                p={2}
                mb={2}
                bgcolor="#e2b4bb"
              >
                <img src={holyLogo} width={100} alt="holyLogo" />
                <Stack textAlign="center" flex={1}>
                  <Typography variant="h1" fontWeight="bold" fontSize={30} color="primary">
                    <b>ANGELS&apos; PARADISE</b>
                  </Typography>
                  <Typography variant="body1" fontSize={13}>
                    The Kindergarten Experience
                  </Typography>
                  <Typography variant="body1" fontSize={15}>
                    Gandhinagar, Dombivli (E)
                  </Typography>
                </Stack>
                <div style={{ visibility: 'hidden', width: 100 }} />
              </Box>
            )}
            {/* ========== Holy Angels Paradise College ========== */}
            {receiptType === 'ReceiptHolyCollege' && (
              <Box
                className="Main-div"
                flexDirection="column"
                mt={5}
                bgcolor="yellow"
                border={1}
                p={1}
                display="flex"
                alignItems="center"
                mb={2}
              >
                <img src={holyLogo} width={70} alt="holyLogo" />
                <Stack color={theme.palette.common.black} direction="row" justifyContent="space-between" gap={5}>
                  <Typography variant="subtitle2" fontSize={14}>
                    Affiliated to CBSE
                  </Typography>
                  <Typography variant="subtitle2" fontSize={13}>
                    TRINITY EDUCATIONAL TRUST&apos;S
                  </Typography>
                  <Typography variant="subtitle2" fontSize={14}>
                    ISO 9001: 2008 Certified
                  </Typography>
                </Stack>
                <Typography variant="h1" fontWeight="bold" fontSize={30} color="primary">
                  <b>HOLY ANGELS&apos; SCHOOL & Jr. COLLEGE</b>
                </Typography>
                <Typography color={theme.palette.common.black} variant="body1" fontSize={13}>
                  (A Private Unaided Minority Institution)
                </Typography>
                <Typography color={theme.palette.common.black} variant="body1" fontSize={9}>
                  Behind P & T Colony, Nandivli - Gandhinagar, Dombivli (E) Dist. Thane, MAH
                  <EMAIL> Tel: (0251) 2821975, 2821234
                </Typography>
              </Box>
            )}
            {/* ============================ */}
            <Box border={1} p={1} display="flex" justifyContent="space-between" mb={1}>
              <Stack>
                <Typography variant="subtitle1" fontSize={13}>
                  Name : <span className="fw-bold">{studentName}</span>
                </Typography>
                <Typography variant="subtitle1" fontSize={13}>
                  Admission No : <span className="fw-bold">{admissionNo}</span>
                </Typography>
                <Typography variant="subtitle1" fontSize={13}>
                  Class Name : <span className="fw-bold">{className}</span>
                </Typography>
              </Stack>
              <Stack>
                <Typography variant="subtitle1" fontSize={13}>
                  Date : <span className="fw-bold">{formattedDate}</span>
                </Typography>
                <Typography variant="subtitle1" fontSize={13}>
                  Receipt No : <span className="fw-bold">{receiptNo}</span>
                </Typography>
                <Typography variant="subtitle1" fontSize={13}>
                  Contact No : <span className="fw-bold">{contactNo}</span>
                </Typography>
              </Stack>
            </Box>
            {/* <Divider sx={{ my: 1 }} /> */}
            <Box
              className="scrollbar"
              sx={{
                // height: 'calc(100vh - 320px)',
                overflow: 'auto',
                mb: 2,
                // '&::-webkit-scrollbar': {
                //   width: '0px',
                // },
              }}
            >
              {/* <TableContainer sx={{ height: 'calc(100vh - 470px)' }}> */}
              <TableContainer>
                <Table stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ '&:first-of-type': { paddingLeft: 0 } }}>Fee Title</TableCell>
                      <TableCell>Term Title</TableCell>
                      <TableCell align="right">Amount (INR)</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {termfeepaid?.map((item) => (
                      <TableRow key={item.receiptDetailsId}>
                        <TableCell sx={{ '&:first-of-type': { paddingLeft: 0 } }} color="secondary">
                          {item.feeTitle}
                        </TableCell>
                        <TableCell color="secondary"> {item.termTitle}</TableCell>
                        <TableCell align="right"> {item.totalAmount}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              <Box sx={{ borderTop: '2px dashed gray' }} py={3} display="flex" justifyContent="space-between">
                <Typography variant="body2" fontWeight={700}>
                  Total Amount Paid
                </Typography>
                <Typography variant="body2" fontWeight={700}>
                  {/* {grandTotal} Display total amount */}
                  {totalAmountPaid.toLocaleString()}
                </Typography>
              </Box>
              <TableContainer sx={{ mb: 2 }}>
                <Table stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ '&:first-of-type': { paddingLeft: 0 } }}>Payment Mode</TableCell>
                      <TableCell>Reference No</TableCell>
                      <TableCell align="right">Amount</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {paymodeData?.map((item) => (
                      <TableRow key={item.paymentModeId}>
                        <TableCell sx={{ '&:first-of-type': { paddingLeft: 0 } }} color="secondary">
                          {item.paymentType}
                        </TableCell>
                        <TableCell> {item.chequeOrDDNo}</TableCell>
                        <TableCell align="right"> {item.amount}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              <Stack className="amount_words" maxWidth={400}>
                <Typography variant="body2" pb={1} color="secondary">
                  Total amount in words :&nbsp;
                  <span
                    className="fw-bold"
                    style={{ color: isLight ? theme.palette.common.black : theme.palette.common.white }}
                  >
                    {grandTotalWords}
                  </span>
                </Typography>
              </Stack>
            </Box>
          </Box>
        </Box>
      </Box>
    </ReceiptPDFRoot>
    // </Page>
  );
};
