// components/SchoolSelector.tsx
import React from 'react';
import { MenuItem, Select, SelectChangeEvent, FormControl, InputLabel } from '@mui/material';
import { useSchool, School } from '@/contexts/SchoolContext';
import passdailLogo from '@/assets/SchoolLogos/logo-small.svg';
import holyLogo from '@/assets/SchoolLogos/HolyLogo.jpeg';
import carmel<PERSON>ogo from '@/assets/SchoolLogos/CarmelLogo.png';
import thereseLogo from '@/assets/SchoolLogos/StthereseLogo.png';
import thomasLogo from '@/assets/SchoolLogos/StThomasLogo.png';
import nirmalaLogo from '@/assets/SchoolLogos/NirmalaLogo.png';
import MIMLogo from '@/assets/SchoolLogos/MIMLogo.png';
import alFitrahLogo from '@/assets/SchoolLogos/alFitrahLogo.png';

const schoolList: School[] = [
  {
    schoolId: '1',
    schoolName: 'Passdaily',
    code: 'SS',
    schoolLogo: passdailLogo,
    infoCardData: [
      {
        id: 1,
        title: 'Passdaily',
        content: 'For students seeking admissions from Nursery, Jr. kg & Sr. kg.',
      },
      {
        id: 2,
        title: 'Passdaily School',
        content: 'For students seeking admissions from standard 1st to standard 10.',
      },
      {
        id: 3,
        title: 'Passdaily College',
        content: 'Passdaily Junior College to Class XI Science & Commerce.',
      },
    ],
  },
  {
    schoolId: '2',
    schoolName: 'Holy Angels School',
    code: 'GH',
    schoolLogo: holyLogo,
    infoCardData: [
      {
        id: 1,
        title: 'Angels Paradise',
        content: 'For students seeking admissions from Nursery, Jr. kg & Sr. kg.',
      },
      {
        id: 2,
        title: 'Holy Angels School',
        content: 'For students seeking admissions from standard 1st to standard 10.',
      },
      {
        id: 3,
        title: 'Holy Angels College',
        content: 'Holy Angels’ Junior College to Class XI Science & Commerce.',
      },
    ],
  },
  {
    schoolId: '3',
    schoolName: 'Carmel  Convent',
    code: 'RA',
    schoolLogo: carmelLogo,
    infoCardData: [
      {
        id: 1,
        title: 'Our Vision',
        content:
          'To mould every prospective potential into dynamic , individualistic and the most pursued citizens of the country to be independent , lifelong who strive and encourage for excellence and becomes responsible student of our global and natural environment.',
      },
      {
        id: 2,
        title: 'Our Mission',
        content:
          'To create an educational world in which children of all types and from all corners of the society have an opportunity for Qualitative education.',
      },
      {
        id: 3,
        title: 'MOTTO Love & Service',
        content:
          'The circle signifies the universe. The star stands for the star of the sea,OUR LADY of MOUNT CARMEL to whom the school is dedicated .The lamp tells us that the students should be lamps radiating light to millions who are still in darkness. Our Alma Mater wants her students to be torch bearers of LOVE AND SERVICE.',
      },
    ],
  },
  {
    schoolId: '4',
    schoolName: 'St Therese',
    code: 'RA',
    schoolLogo: thereseLogo,
    infoCardData: [
      {
        id: 1,
        title: 'Our Vision',
        content:
          'We envision a holistic transformation of young minds and for this, a kind of enrichment that would ultimately engulf our society and nation as a whole.',
      },
      {
        id: 2,
        title: 'Our Mission',
        content:
          'We dedicate ourselves to reinvent ourselves as teachers and educators for the august mission of enriching the young for academic excellence, development of skills and character formation, based on Love of God and service to humanity as our motto.',
      },
      {
        id: 3,
        title: 'Motto',
        content:
          'The motto of our school is LOVE, TRUTH AND SERVICE. And the emblem, in brief, represents the ideal bounding among the school community, all of whom are pledged to live up to it.',
      },
    ],
  },
  {
    schoolId: '5',
    schoolName: 'St Thomas',
    code: 'RA',
    schoolLogo: thomasLogo,
    infoCardData: [
      {
        id: 1,
        title: '',
        content:
          'It is with great pleasure that I welcome you to our school website. I am happy to inform that our school has completed beyond 25 glorious years in Dombivli. The Management and the staff are grateful to God and thank the Almighty for His manifold blessings in helping us to achieve great heights.',
      },
      {
        id: 2,
        title: '',
        content:
          'As Principal I am hugely impressed by the commitment of the management and the staff to the provision of an excellent all-round education for our students in our state of the art facilities. As a team working together, we strongly promote academic achievement among our students. The cultural, sporting and other successes of all of our students and staff are also proudly celebrated together.',
      },
      {
        id: 3,
        title: '',
        content:
          'St. Thomas School, is an innovative school drawing on the talents and skills of staff, students and parents to provide a host of educational programmes and projects. Wholesome participation is encouraged in the extensive range of extra-curricular activities and care is also taken to ensure the well-being and happiness of each and every student in the school.',
      },
    ],
  },
  {
    schoolId: '6',
    schoolName: 'Nirmala',
    code: 'RA',
    schoolLogo: nirmalaLogo,
    infoCardData: [
      {
        id: 1,
        title: 'Our Vision',
        content:
          'To mould every prospective potential into dynamic, individualistic and the most pursued citizens of the country to be independent, lifelong who strive and encourage for responsible student of our global and natural environment.',
      },
      {
        id: 2,
        title: 'Mission & Mission Statement',
        content:
          'To create an educational world in which children of all types and from all corners of the society have an opportunity for qualitative education. “ The steadfast love of the Lord never ceases, His mercies never come to an end, they are new every morning, great is your faithfulness.” -Holy Bible.',
      },
      {
        id: 3,
        title: 'School Motto',
        content:
          ' To create an educational world in which children of all types and from all corners of the society have an opportunity for qualitative education. “ The steadfast love of the Lord never ceases, His mercies never come to an end, they are new every morning, great is your faithfulness.” -Holy Bible.',
      },
    ],
  },
];

const SchoolSelector = () => {
  const { selectedSchool, setSelectedSchool } = useSchool();

  const handleChange = (event: SelectChangeEvent) => {
    const school = schoolList.find((s) => s.schoolId === event.target.value);
    if (school) setSelectedSchool(school);
  };

  return (
    <FormControl fullWidth>
      <Select sx={{ width: 150 }} fullWidth value={selectedSchool?.schoolId || '1'} onChange={handleChange}>
        {schoolList.map((school) => (
          <MenuItem key={school.schoolId} value={school.schoolId}>
            {school.schoolName}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default SchoolSelector;
