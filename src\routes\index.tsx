/* eslint-disable prettier/prettier */
import { useRoutes } from 'react-router-dom';
import AuthGuard from '@/Guards/AuthGuard';
import useAuth from '@/hooks/useAuth';
import MainLayout from '@/layouts/MainLayout';

// import OnlinePayment from '@/Parent-Side/features/Fees/OnlinePayment';
import TestLogin from '@/features/login/TestLogin';
import { authRoutes } from '@/routes/authRoutes';
import { dashboardRoutes } from '@/routes/dashboardRoutes';
import { attendanceRoutes } from '@/routes/attendanceRoutes';
import { messageRoutes } from '@/routes/messageRoutes';
import { voiceRoutes } from '@/routes/voiceRoutes';
import { notificationRoutes } from '@/routes/appNotificationRoutes';
import { academicsRoutes } from '@/routes/academicsRoutes';
import { studentsRoutes } from '@/routes/studentsRoutes';
import { staffRoutes } from '@/routes/staffRoutes';
import { staffAttendanceRoutes } from '@/routes/staffAttendanceRoutes';
import { manageFeeRoutes } from '@/routes/manageFeeRoutes';
import { manageBusFeesRoutes } from '@/routes/manageBusFeesRoutes';
import { feeAlertRoutes } from '@/routes/feeAlertRoutes';
import { enquiryRoutes } from '@/routes/enquiryRoutes';
import { trackingRoutes } from '@/routes/trackingRoutes';
import { timetableRoutes } from '@/routes/timetableRoutes';
import { publishResultRoutes } from '@/routes/publishResultRoutes';
import { examCenterRoutes } from '@/routes/examCenterRoutes';
import { albumRoutes } from '@/routes/albumRoutes';
import { paymentDetailsRoutes } from '@/routes/paymentDetailsRoutes';
import { libraryManagementRoutes } from '@/routes/libraryManagementRoutes';
import { adminControlsRoutes } from '@/routes/adminControlsRoutes';
import { storeManagementRoutes } from '@/routes/storeManagementRoutes';
import { generalSettingsRoutes } from './generalSettingsRoutes';
import { groupSettingsRoutes } from './groupSettingsRoutes';
import { liveClassRoutes } from './liveClassRoutes';
import { schoolCalendarRoutes } from './schoolCalendarRoutes';
import { onlineVideoClassRoutes } from './onlineVideoClassRoutes';
import { onlineExamRoutes } from './onlineExamRoutes';
import { assignmentRoutes } from './assignmentRoutes';
import { reportRoutes } from './reportRoutes';
import { ptaForumRoutes } from './ptaForumRoutes';
import { certificateRoutes } from './certificateRoutes';
import { tcandCcRoutes } from './tcandccRoutes';
import { schoolPlanRoutes } from './schoolPlanRoutes';
// Parent
import { parentDashboardRoutes } from './parentRoutes/parentDashboardRoutes';
// import TransactionResponse from '@/Parent-Side/features/Fees/TransactionResponse';
import { parentPayFeeRoutes } from './parentRoutes/parentPayFeeRoutes';
import { parentLiveClassRoutes } from './parentRoutes/parentLiveClassRoutes';
import { parentOnlineVideo } from './parentRoutes/parentOnlineVideo';
import { parentStudyMaterialsRoutes } from './parentRoutes/parentStudyMaterialsRoutes';
import { socialRoutes } from './socialRoutes';
import { registrationRoutes } from './registrationRoutes';
import { parentAssignmentsRoutes } from './parentRoutes/parentAssignmentsRoutes';
import { parentDescriptiveExamRoutes } from './parentRoutes/parentDescriptiveExamRoutes';
import { parentEnquiryRoutes } from './parentRoutes/parentEnquiryRoutes';
import { parentLeaveRoutes } from './parentRoutes/parentLeaveRoutes';
import { parentAbsentRoutes } from './parentRoutes/parentAbsentRoutes';
import { parentObjectiveExamRoutes } from './parentRoutes/parentObjectiveExamRoutes';
import { parentLibraryRoutes } from './parentRoutes/parentObjectiveExamRoutes copy';
import { parentGalleryRoutes } from './parentRoutes/parentGalleryRoutes';
import { parentCalendarRoutes } from './parentRoutes/parentCalendarRoutes';
import { parentProgressReportRoutes } from './parentRoutes/parentProgressReportRoutes';
import { parentBusTrackRoutes } from './parentRoutes/parentBusTrackRoutes';
import { parentOnlinePayFeeRoutes } from './parentRoutes/parentOnlinePayFeeRoutes';

function Routes() {
  const { parentMode } = useAuth();
  const routes = useRoutes([
    ...authRoutes,
    ...parentOnlinePayFeeRoutes,
    {
      path: '/authenticateLogin',
      element: (
        // <AuthGuard>
          <TestLogin />
        // </AuthGuard>
      ),
    },
    {
      path: '/',
      element: (
        <AuthGuard>
          <MainLayout />
        </AuthGuard>
      ),
      children: parentMode
        ? [
            ...parentDashboardRoutes,
            ...parentPayFeeRoutes,
            ...parentLiveClassRoutes,
            ...parentOnlineVideo,
            ...parentStudyMaterialsRoutes,
            ...parentAssignmentsRoutes,
            ...parentDescriptiveExamRoutes,
            ...parentEnquiryRoutes,
            ...parentLeaveRoutes,
            ...parentAbsentRoutes,
            ...parentObjectiveExamRoutes,
            ...parentLibraryRoutes,
            ...parentGalleryRoutes,
            ...parentCalendarRoutes,
            ...parentProgressReportRoutes,
            ...parentBusTrackRoutes,
          ]
        : [
            ...dashboardRoutes,
            ...registrationRoutes,
            ...attendanceRoutes,
            ...messageRoutes,
            ...voiceRoutes,
            ...notificationRoutes,
            ...academicsRoutes,
            ...studentsRoutes,
            ...staffRoutes,
            ...staffAttendanceRoutes,
            ...manageFeeRoutes,
            ...manageBusFeesRoutes,
            ...feeAlertRoutes,
            ...enquiryRoutes,
            ...trackingRoutes,
            ...timetableRoutes,
            ...publishResultRoutes,
            ...examCenterRoutes,
            ...albumRoutes,
            ...paymentDetailsRoutes,
            ...libraryManagementRoutes,
            ...adminControlsRoutes,
            ...storeManagementRoutes,
            ...generalSettingsRoutes,
            ...groupSettingsRoutes,
            ...schoolCalendarRoutes,
            ...liveClassRoutes,
            ...onlineVideoClassRoutes,
            ...assignmentRoutes,
            ...onlineExamRoutes,
            ...reportRoutes,
            ...ptaForumRoutes,
            ...certificateRoutes,
            ...tcandCcRoutes,
            ...socialRoutes,
            ...schoolPlanRoutes,
          ],
    },
    // {
    //   path: '/parent',
    //   element: (
    //     <AuthGuard>
    //       <MainLayout />
    //     </AuthGuard>
    //   ),
    //   children:
    //   [
    //     ...parentDashboardRoutes,
    //     ...parentPayFeeRoutes,
    //   ],
    // },
  ]);

  return routes;
}

export default Routes;
