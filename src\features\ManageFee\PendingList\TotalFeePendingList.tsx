/* eslint-disable new-cap */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Card,
  Tooltip,
  IconButton,
  Collapse,
  FormControl,
  Select,
  SelectChangeEvent,
  MenuItem,
  useTheme,
  Avatar,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import {
  getClassSectionsData,
  getFeePendingListData,
  getFeePendingListStatus,
  getManageFeeclassListData,
  getStudentFilterData,
  getYearData,
} from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import {
  fetchClassList,
  fetchClassSections,
  fetchFeePendingList,
  fetchStudentsFilter,
} from '@/store/ManageFee/manageFee.thunks';
import CurrencyRupeeIcon from '@mui/icons-material/CurrencyRupee';
import { GetFeePendingListDataType, GetFeePendingListRequestType } from '@/types/ManageFee';
import useSettings from '@/hooks/useSettings';
import { useReactToPrint } from 'react-to-print';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import * as XLSX from 'xlsx';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';
import DTVirtuoso from '@/components/shared/RND/DataTableVirtuoso';

// const tableRef = useRef<HTMLDivElement>(null);

// const handlePrint = useReactToPrint({
//   content: () => tableRef.current,
// });

// const exportPDF = () => {
//   const input = tableRef.current;
//   html2canvas(input).then((canvas) => {
//     const imgData = canvas.toDataURL('image/png');
//     const pdf = new jsPDF();

//     // Calculate the dimensions of the PDF page
//     const pdfWidth = pdf.internal.pageSize.getWidth();
//     const pdfHeight = pdf.internal.pageSize.getHeight();

//     // Calculate the aspect ratio of the canvas image
//     const aspectRatio = canvas.width / canvas.height;
//     let imgWidth = pdfWidth;
//     let imgHeight = pdfWidth / aspectRatio;

//     if (imgHeight > pdfHeight) {
//       imgHeight = pdfHeight;
//       imgWidth = pdfHeight * aspectRatio;
//     }

//     pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
//     pdf.save('table.pdf');
//   });
// };

// const exportExcel = () => {
//   const ws = XLSX.utils.json_to_sheet(feePaidListData);
//   const wb = XLSX.utils.book_new();
//   XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
//   XLSX.writeFile(wb, 'table.xlsx');
// };

//  sx={{ display: 'none' }}
//  ref={tableRef}
//         <Box pt={2} display="flex" width="100%" sx={{ justifyContent: { xs: 'center', sm: 'right' } }}>
//           <Stack spacing={2} direction="row">
//             <Button variant="contained" size="medium" color="success" onClick={exportExcel}>
//               Excel
//             </Button>
//             <Button variant="contained" size="medium" color="info" onClick={exportPDF}>
//               Pdf
//             </Button>
//             <Button variant="contained" size="medium" onClick={handlePrint}>
//               Print
//             </Button>
//           </Stack>
//         </Box>

const TotalFeePendingListRoot = styled.div`
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid ${(props) => props.theme.palette.grey[200]};
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
        .reminder_btn {
          padding: 0px 1px 0px 1px;
          font-size: 13px;
        }
      }
    }
    @media screen and (min-width: 992px) {
      .MuiTableCell-head:nth-child(1) {
        padding-left: 4px;
        z-index: 11;
        position: sticky;
        left: 0;
        background-color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
      }
      .MuiTableCell-head:nth-child(2) {
        z-index: 11;
        position: sticky;
        left: 45px;
        background-color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
      }
      .MuiTableCell-head:nth-child(3) {
        z-index: 11;
        position: sticky;
        left: 255px;
        background-color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
      }
      .MuiTableCell-body:nth-child(1) {
        z-index: 1;
        position: sticky;
        left: 0;
        background-color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
      }
      .MuiTableCell-body:nth-child(2) {
        z-index: 1;
        position: sticky;
        left: 45px;
        background-color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
      }
      .MuiTableCell-body:nth-child(3) {
        z-index: 1;
        position: sticky;
        left: 255px;
        background-color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
      }
    }
  }
`;

export default function TotalFeePendingList() {
  const tableRef = useRef<HTMLDivElement>(null);
  const isLight = useSettings().themeMode === 'light';
  const theme = useTheme();
  const [showFilter, setShowFilter] = useState(true);
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const adminId: number = user ? user.accountId : 0;

  const academicId = 10;
  const [academicYearFilter, setAcademicYearFilter] = useState(0);
  const [feeTypeFilter, setFeeTypeFilter] = useState(0);
  const YearData = useAppSelector(getYearData);
  const feePendingListData = useAppSelector(getFeePendingListData);
  const feePendingListStatus = useAppSelector(getFeePendingListStatus);
  const ClassSectionsData = useAppSelector(getClassSectionsData);
  const classListData = useAppSelector(getManageFeeclassListData);
  const studentFilterData = useAppSelector(getStudentFilterData);
  const [currentPage, setCurrentPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const [classSectionsFilter, setClassSectionsFilter] = useState(0);
  const [classFilter, setClassFilter] = useState(0);
  const [studentsFilter, setStudentsFilter] = useState(-1);

  const currentFeePendingListRequest = useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId: feeTypeFilter,
      sectionId: classSectionsFilter,
      classId: classFilter,
      studentId: studentsFilter,
    }),
    [adminId, academicYearFilter, classFilter, classSectionsFilter, studentsFilter, feeTypeFilter]
  );
  const loadFeePendingList = useCallback(
    async (request: GetFeePendingListRequestType) => {
      try {
        const response = await dispatch(fetchFeePendingList(request)).unwrap();

        if (response) {
          console.log('response::::----', response);
        }
      } catch (error) {
        console.error('Failed to load term fee list:', error);
      }
    },
    [dispatch]
  );
  useEffect(() => {
    dispatch(fetchYearList(adminId));
    dispatch(fetchClassSections({ adminId, academicId }));
    dispatch(fetchClassList({ adminId, academicId, sectionId: classSectionsFilter }));
    dispatch(
      fetchStudentsFilter({
        adminId,
        academicId,
        sectionId: classSectionsFilter,
        classId: classFilter,
        feeTypeId: feeTypeFilter,
      })
    );
    if (feePendingListStatus === 'idle') {
      loadFeePendingList(currentFeePendingListRequest);
    }
    console.log('studentsFilter::::----', studentsFilter);
  }, [
    feePendingListStatus,
    adminId,
    dispatch,
    classSectionsFilter,
    studentsFilter,
    loadFeePendingList,
    classFilter,
    currentFeePendingListRequest,
    feeTypeFilter,
  ]);

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadFeePendingList({ ...currentFeePendingListRequest, academicId: parseInt(e.target.value, 10) });
    setClassSectionsFilter(0);
    setClassFilter(0);
    setStudentsFilter(-1);
  };

  const handleFeeTypeChange = (e: SelectChangeEvent) => {
    const feeTypeId = parseInt(e.target.value, 10);
    setFeeTypeFilter(feeTypeId);
    loadFeePendingList({ ...currentFeePendingListRequest, feeTypeId: parseInt(e.target.value, 10) });
  };

  const handleClassSectionChange = (e: SelectChangeEvent) => {
    setClassSectionsFilter(
      parseInt(ClassSectionsData.filter((item) => item.sectionId === e.target.value)[0].sectionId, 10)
    );
    loadFeePendingList({ ...currentFeePendingListRequest, sectionId: parseInt(e.target.value, 10) });
    setClassFilter(0);
    setStudentsFilter(-1);
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    setClassFilter(parseInt(classListData.filter((item) => item.classId === e.target.value)[0].classId, 10));
    loadFeePendingList({ ...currentFeePendingListRequest, classId: parseInt(e.target.value, 10) });
    setStudentsFilter(-1);
  };

  const handleStudentChange = (e: SelectChangeEvent) => {
    setStudentsFilter(parseInt(studentFilterData.filter((item) => item.studentId === e.target.value)[0].studentId, 10));
    loadFeePendingList({ ...currentFeePendingListRequest, studentId: parseInt(e.target.value, 10) });
  };

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setClassSectionsFilter(0);
      setClassFilter(0);
      setAcademicYearFilter(0);
      setStudentsFilter(-1);
      setFeeTypeFilter(0);
      loadFeePendingList({
        adminId,
        academicId,
        feeTypeId: 0,
        sectionId: 0,
        classId: 0,
        studentId: -1,
      });
    },
    [loadFeePendingList, adminId, academicId]
  );

  // Pagination handlers
  const handlePageChange = useCallback(
    (event: any, newPage: any) => {
      setCurrentPage(newPage); // Update current page
    },
    [setCurrentPage]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: any) => {
      setRowsPerPage(parseInt(event.target.value, 10)); // Update rows per page
      // Reset to first page when changing rows per page
    },
    [setRowsPerPage]
  );

  const paginatedData = feePendingListData.slice(currentPage * rowsPerPage, currentPage * rowsPerPage + rowsPerPage);

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: currentPage,
      pageSize: rowsPerPage,
      totalRecords: feePendingListData.length,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, currentPage, rowsPerPage, feePendingListData]
  );

  const feePaidListColumns: DataTableColumn<GetFeePendingListDataType>[] = useMemo(
    () => [
      {
        name: '',
        headerLabel: 'SL.No',
        renderCell: (_, index) => {
          return (
            <Typography fontSize={13} variant="subtitle1">
              {index + 1}
            </Typography>
          );
        },
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack minWidth={200} direction="row" alignItems="center" gap={1}>
              <Avatar src={row?.image} alt={row.studentName} />
              <Typography variant="subtitle2" fontSize={13}>
                {row.studentName}
              </Typography>
            </Stack>
          );
        },
      },
      {
        name: 'admissionNo',
        headerLabel: 'Admission No',
        renderCell: (row) => {
          return (
            <Typography minWidth={100} fontSize={13} variant="subtitle1">
              {row.admissionNo}
            </Typography>
          );
        },
      },
      {
        name: 'className',
        headerLabel: 'Class',
        renderCell: (row) => {
          return (
            <Typography minWidth={100} fontSize={13} variant="subtitle1">
              {row.className}
            </Typography>
          );
        },
      },
      {
        name: 'gender',
        headerLabel: 'Gender',
        renderCell: (row) => {
          return (
            <Typography minWidth={100} fontSize={13} variant="subtitle1">
              {row.gender}
            </Typography>
          );
        },
      },
      {
        name: 'guardianName',
        headerLabel: 'Guardian Name',
        renderCell: (row) => {
          return (
            <Typography minWidth={200} fontSize={13} variant="subtitle1">
              {row.guardianName}
            </Typography>
          );
        },
      },
      {
        name: 'guardianNumber',
        headerLabel: 'Guardian Number',
        renderCell: (row) => {
          return (
            <Typography minWidth={150} fontSize={13} variant="subtitle1">
              {row.guardianNumber}
            </Typography>
          );
        },
      },
      {
        name: 'totalFee',
        headerLabel: 'Total Fee',
        renderCell: (row) => {
          return (
            <Stack minWidth={100} direction="row" alignItems="center">
              <CurrencyRupeeIcon sx={{ fontSize: '13px' }} />
              <Typography fontSize={13} variant="subtitle1">
                {row.totalFee}
              </Typography>
            </Stack>
          );
        },
      },
      {
        name: 'paid',
        headerLabel: 'Paid',
        renderCell: (row) => {
          return (
            <Stack minWidth={50} color={theme.palette.success.main} direction="row" alignItems="center">
              <CurrencyRupeeIcon sx={{ fontSize: '13px' }} />
              <Typography fontSize={13} variant="subtitle1">
                {row.paid}
              </Typography>
            </Stack>
          );
        },
      },
      {
        name: 'balance',
        headerLabel: 'Balance',
        renderCell: (row) => {
          return (
            <Stack minWidth={50} color={theme.palette.error.main} direction="row" alignItems="center">
              <CurrencyRupeeIcon sx={{ fontSize: '13px' }} />
              <Typography fontSize={13} variant="subtitle1">
                {row.balance}
              </Typography>
            </Stack>
          );
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack minWidth={120}>
              <Button
                className="reminder_btn"
                size="small"
                color="warning"
                // startIcon={<PrintIcon />}
                variant="outlined"
              >
                Send Reminder
              </Button>
            </Stack>
          );
        },
      },
    ],
    [theme]
  );
  const feePaidListColumnsForExport: DataTableColumn<GetFeePendingListDataType>[] = useMemo(
    () => [
      {
        name: '',
        headerLabel: 'SL.No',
        renderCell: (_, index) => {
          return (
            <Typography fontSize={13} variant="subtitle1">
              {index + 1}
            </Typography>
          );
        },
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" alignItems="center" gap={1}>
              <Avatar src={row?.image} alt={row.studentName} />
              <Typography variant="subtitle2" fontSize={13}>
                {row.studentName}
              </Typography>
            </Stack>
          );
        },
      },
      {
        name: 'admissionNo',
        headerLabel: 'Admission No',
        renderCell: (row) => {
          return (
            <Typography fontSize={13} variant="subtitle1">
              {row.admissionNo}
            </Typography>
          );
        },
      },
      {
        name: 'className',
        headerLabel: 'Class',
        renderCell: (row) => {
          return (
            <Typography fontSize={13} variant="subtitle1">
              {row.className}
            </Typography>
          );
        },
      },
      {
        name: 'gender',
        headerLabel: 'Gender',
        renderCell: (row) => {
          return (
            <Typography fontSize={13} variant="subtitle1">
              {row.gender}
            </Typography>
          );
        },
      },
      {
        name: 'guardianName',
        headerLabel: 'Guardian Name',
        renderCell: (row) => {
          return (
            <Typography fontSize={13} variant="subtitle1">
              {row.guardianName}
            </Typography>
          );
        },
      },
      {
        name: 'guardianNumber',
        headerLabel: 'Guardian Number',
        renderCell: (row) => {
          return (
            <Typography fontSize={13} variant="subtitle1">
              {row.guardianNumber}
            </Typography>
          );
        },
      },
      {
        name: 'totalFee',
        headerLabel: 'Total Fee',
        renderCell: (row) => {
          return (
            <Stack direction="row" alignItems="center">
              <CurrencyRupeeIcon sx={{ fontSize: '13px' }} />
              <Typography fontSize={13} variant="subtitle1">
                {row.totalFee}
              </Typography>
            </Stack>
          );
        },
      },
      {
        name: 'paid',
        headerLabel: 'Paid',
        renderCell: (row) => {
          return (
            <Stack color={theme.palette.success.main} direction="row" alignItems="center">
              <CurrencyRupeeIcon sx={{ fontSize: '13px' }} />
              <Typography fontSize={13} variant="subtitle1">
                {row.paid}
              </Typography>
            </Stack>
          );
        },
      },
      {
        name: 'balance',
        headerLabel: 'Balance',
        renderCell: (row) => {
          return (
            <Stack color={theme.palette.error.main} direction="row" alignItems="center">
              <CurrencyRupeeIcon sx={{ fontSize: '13px' }} />
              <Typography fontSize={13} variant="subtitle1">
                {row.balance}
              </Typography>
            </Stack>
          );
        },
      },
    ],
    [theme]
  );
  const getRowKey = useCallback((row: GetFeePendingListDataType, index: number | undefined) => index, []);

  const handlePrint = useReactToPrint({
    content: () => tableRef.current,
  });

  const exportPDF = () => {
    const input: any = tableRef.current;
    html2canvas(input).then((canvas) => {
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF();

      // Calculate the dimensions of the PDF page
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();

      // Calculate the aspect ratio of the canvas image
      const aspectRatio = canvas.width / canvas.height;
      let imgWidth = pdfWidth;
      let imgHeight = pdfWidth / aspectRatio;

      if (imgHeight > pdfHeight) {
        imgHeight = pdfHeight;
        imgWidth = pdfHeight * aspectRatio;
      }

      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
      pdf.save('table.pdf');
    });
  };

  const exportExcel = () => {
    const ws = XLSX.utils.json_to_sheet(feePendingListData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, 'table.xlsx');
  };

  return (
    <Page title="Total Fees Pending List">
      <TotalFeePendingListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Box display="flex" pb={0.5} justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Total Fee Pending List
            </Typography>
            <Stack direction="row" alignItems="center">
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ ml: 1 }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton aria-label="delete" color="primary" sx={{ ml: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Stack>
          </Box>

          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={{ xxl: 2, sm: 16 }} pt={1} container columnSpacing={2} alignItems="end">
                  <Grid item xxl={2} xl={3} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={3} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Type
                      </Typography>
                      <Select
                        labelId="feeTypeFilter"
                        id="feeTypeFilterSelect"
                        value={feeTypeFilter.toString()}
                        onChange={handleFeeTypeChange}
                        placeholder="Select Year"
                      >
                        <MenuItem sx={{ display: 'none' }} value={0}>
                          Select Type
                        </MenuItem>
                        {FEE_TYPE_ID_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={3} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Section
                      </Typography>
                      <Select
                        labelId="classSectionsFilter"
                        id="classSectionsFilterSelect"
                        value={classSectionsFilter?.toString()}
                        onChange={handleClassSectionChange}
                        placeholder="Select Section"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Section
                        </MenuItem>
                        {ClassSectionsData.map((opt) => (
                          <MenuItem key={opt.sectionId} value={opt.sectionId}>
                            {opt.sectionName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={3} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        disabled={classListData.length === 0}
                        labelId="classFilter"
                        id="classFilter"
                        value={classFilter?.toString()}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[700],
                          },
                        }}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Class
                        </MenuItem>
                        {classListData.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={1.8} xl={3} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student
                      </Typography>
                      <Select
                        disabled={studentFilterData.length === 0}
                        labelId="studentsFilter"
                        id="studentsFilter"
                        value={studentsFilter?.toString()}
                        onChange={handleStudentChange}
                        placeholder="Select Student"
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[700],
                          },
                        }}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={-1} sx={{ display: 'none' }}>
                          Select Student
                        </MenuItem>
                        {studentFilterData.map((opt) => (
                          <MenuItem key={opt.studentId} value={opt.studentId}>
                            {opt.studentName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button sx={{ mt: { xs: 2 } }} type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper sx={{ display: 'none' }}>
              <div ref={tableRef}>
                <DataTable
                  columns={feePaidListColumnsForExport}
                  data={feePendingListData}
                  getRowKey={getRowKey}
                  fetchStatus={feePendingListStatus}
                  showHorizontalScroll
                />
              </div>
            </Paper>
            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DTVirtuoso
                tableStyles={{ minWidth: { xs: '1100px', xl: '1300px', xxl: '100%' } }}
                columns={feePaidListColumns}
                data={paginatedData}
                getRowKey={getRowKey}
                fetchStatus={feePendingListStatus}
                showHorizontalScroll
                PaginationProps={pageProps}
                allowPagination
              />
            </Paper>
          </div>
        </Card>
        <Box pt={2} display="flex" width="100%" sx={{ justifyContent: { xs: 'center', sm: 'right' } }}>
          <Stack spacing={2} direction="row">
            <Button
              disabled={feePendingListData.length === 0}
              variant="contained"
              size="medium"
              color="success"
              onClick={exportExcel}
            >
              Excel
            </Button>
            <Button
              disabled={feePendingListData.length === 0}
              variant="contained"
              size="medium"
              color="info"
              onClick={exportPDF}
            >
              Pdf
            </Button>
            <Button disabled={feePendingListData.length === 0} variant="contained" size="medium" onClick={handlePrint}>
              Print
            </Button>
          </Stack>
        </Box>
      </TotalFeePendingListRoot>
    </Page>
  );
}
