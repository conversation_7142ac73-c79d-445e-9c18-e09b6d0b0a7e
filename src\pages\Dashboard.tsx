import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import LinkCardRoot from '@/features/Dashboard/LinkCard';
import styled from 'styled-components';
import { Box, Card, Divider, Grid, Stack } from '@mui/material';
import Birthday from '@/features/Dashboard/Birthday/Birthday';
import DownloadLinkCard from '@/features/Dashboard/DownloadLinkCard';
import StatusCard from '@/features/Dashboard/StatusCard';
import Events from '@/features/Dashboard/Events';
import Graph from '@/features/Dashboard/Graph';
import Timetable from '@/features/Dashboard/TimetableCard';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import Statistic from '@/features/Dashboard/Statistic';
import OnlineVideo from '@/features/Dashboard/OnlineVideo';
import BottomBirthdayDrawer from '@/features/Dashboard/RightSideDrawer';
import useAuth from '@/hooks/useAuth';
import { useEffect } from 'react';
import { fetchClassList } from '@/store/Dashboard/dashboard.thunks';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { getClassData, getClassStatus } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import SmallCalendar from '@/components/Calendar/SmallCalendar';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';
import SchoolSelector from '@/components/SchoolsList';

const MainDashboardRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});
  /* padding: 1rem; */
  overflow: hidden;
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.grey[100] : props.theme.palette.grey[800]};
  @media ${breakPointsMaxwidth.sm} {
    /* min-height: calc(100vh  - 1rem); */
  }

  .status-card {
  }
  .Bday-Card {
    height: calc(100vh - 112px);
    width: 290px;
    position: fixed;
    right: 0;
    @media screen and (max-width: 1300px) {
      width: 250px;
    }
  }

  .right-side::-webkit-scrollbar {
    display: none;
  }
  .center::-webkit-scrollbar {
    display: none;
  }
  .events-statistic {
  }
  .darkmode-color {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }
  .main {
    width: calc(100% - 300px);
    @media screen and (max-width: 1300px) {
      width: calc(100% - 265px);
    }
    @media ${breakPointsMaxwidth.lg} {
      width: 100%;
    }
  }
`;
function MainDashboard() {
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const adminId: number | undefined = user?.accountId;
  console.log(adminId);

  const ClassData = useAppSelector(getClassData);

  useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch]);

  useEffect(() => {
    dispatch(fetchClassList(adminId));
  }, [dispatch, adminId]);
  return (
    <Page title="Dashboard">
      <MainDashboardRoot>

        <Stack
          className="main"
          sx={{
            '&::-webkit-scrollbar': {
              width: '0px', // Adjust the width of the scrollbar as per your requirement
            },
          }}
        >
          <Box>
            <LinkCardRoot />
            <Card elevation={0} sx={{ margin: '0rem .5rem 0rem .5rem' }} className="darkmode-color">
              <StatusCard adminId={adminId} />
            </Card>

            <Grid container>
              <Grid item xl={8.5} xs={12} lg={12} className="events-statistic">
                <Card sx={{ margin: '1rem .5rem 1rem .5rem', height: '340px' }} className="darkmode-color">
                  <Statistic title="Fee Statistics" />
                </Card>
                <Card
                  elevation={5}
                  sx={{ margin: '1rem .5rem 1rem .5rem', height: { xs: '390px', sm: '340px' } }}
                  className="darkmode-color"
                >
                  {' '}
                  <Events title="Events" adminId={adminId} />
                </Card>
              </Grid>
              <Grid item xl={3.5} xs={12} lg={12} className="Graph ">
                <Grid container>
                  <Grid item xl={12} lg={6} md={6} sm={6} xs={12}>
                    <Graph />
                  </Grid>
                  <Grid item xl={12} lg={6} md={6} sm={6} xs={12}>
                    <OnlineVideo />
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
            <Card elevation={5} sx={{ margin: '0rem .5rem 1rem .5rem' }} className="darkmode-color">
              <Timetable ClassData={ClassData} />
            </Card>
          </Box>
          {/* ========== right side ========= */}
          <Box
            sx={{
              '&::-webkit-scrollbar': {
                width: '0px', // Adjust the width of the scrollbar as per your requirement
              },
              display: { xs: 'none', lg: 'block ', margin: '1rem .5rem 1rem 0rem' },
              overflow: 'auto',
            }}
            className=" Bday-Card "
          >
            <Card
              elevation={0}
              sx={{ borderBottomLeftRadius: 0, borderBottomRightRadius: 0 }}
              className="darkmode-color"
            >
              <Box p={2}>
                <SmallCalendar />
              </Box>
            </Card>
            {/* <Divider sx={{ mt: '1rem', borderColor: 'grey.500', borderWidth: '2px' }} /> */}
            <Card elevation={0} sx={{ borderRadius: 0, my: 1 }} className="darkmode-color">
              <Birthday />
            </Card>
            <Card elevation={0} sx={{ borderTopLeftRadius: 0, borderTopRightRadius: 0 }}>
              <DownloadLinkCard />
            </Card>
          </Box>
          <Grid
            item
            xl={3}
            lg={4}
            sm={12}
            sx={{ display: { xs: 'block', lg: 'none ' }, position: 'fixed', right: '10px', bottom: '10px' }}
          >
            <BottomBirthdayDrawer />
          </Grid>
        </Stack>
      </MainDashboardRoot>
    </Page>
  );
}

export default MainDashboard;
